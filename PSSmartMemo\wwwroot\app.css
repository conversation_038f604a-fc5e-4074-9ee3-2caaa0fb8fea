
body {
    background-color: #F4F6F9 !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

html, body {
    overscroll-behavior: none;
}

body, p, h1, h2, h3 {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
}

.userprofilecard {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 6px;
    width: 100%;
}

.userprofilecard h3 {
    font-size: 16px;
    color: #013582;
    font-weight: bold;
    line-height: 16px;
    margin: 10px 0;
}

.userprofilecard p {
    color: dimgray;
    font-size: 12px;
    margin: 0;
    margin-bottom: 5px
}

.userlocationcard {
    color: white;
    background: #013582;
    border-radius: 6px;
    width: 80%;
    padding: 4px;
    font-size: 12px;
    text-align: center;
}

.mud-appbar {
    background: #013582 !important;
}

.ticker {
    display: flex;
    height: 50px;

}

.ticker .head {
    background-color: #E5655A;
    width: 170px;
    color: white;
    height: 50px;
    text-align: center;
    /*padding-top:12px;*/
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
}

.ticker .text {
    padding: 3px;
    color: #013582;
    background-color: #E4E4E4;
    font-weight: bold;
    padding: 12px;
    flex: 1;
    height: 50px;
    font-size: 16px;
    padding-left: 20px;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: thin;
    scrollbar-color: #013582 #E4E4E4;
    overflow-y: hidden;


}


.page-panel {
    display: flex;
    justify-content: space-between;
    margin: 6px;
    min-height: 60px;
    align-items: center;
    flex-wrap: wrap;
}


.e-dialog {
    max-height: 90vh !important;
}


.sec-title {
    color: #013582;
    font-size: 1.4rem;
    font-weight: 600;
}

.c-container {
    padding: 5px 10px;
    height: calc(100vh - 65px);
    overflow-x:hidden;
}


.bit-csl-dot > div {
    background-color: #78a8f1 !important;
}

.bit-csl-dot > div.current {
    background-color: #013582 !important;
}

.bit-icon {

    background-color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    padding: 10px;
}

.vision-card {
    display: flex;
    padding: 10px;
    align-items: center;
    gap: 20px;
    height: 159px;
    cursor: pointer;
}

.vision-card div img {
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.vision-card div h3 {
    cursor: pointer;
    margin-bottom: 22px;
    font-size: 20px;
    font-weight: bold;
}

.vision-card div p {
    font-size: 11px;
}

.view-all {
    color: darkblue;
    text-decoration: underline;
    font-weight: bold;
    font-size: 16px;
}

.view-all-container {
    display: flex;
    justify-content: space-between;
    margin-left: 5px;
    margin-right: 10px;
    margin-top: 10px;
    align-items: center
}

.separator {
    height: 1px;
    width: 100%;
    margin-top: 25px;
    margin-bottom: 15px;
    border-top: 1px solid #7777;
}

.item {
    background-color: white;
    width: 33%;
}


.e-dialog .e-dlg-header {
    /*color: white;
    
    font-size: 20px;
    font-weight: normal;*/
}

.e-dialog .e-dlg-header-content {
}


.mud-toolbar-appbar {
    background: url('/images/header-left.png');
    background-repeat: no-repeat;
}

.e-popup.e-popup-open.e-dialog {
    border: 1px solid #013582;
}

.e-dialog .e-dlg-header-content {
    background-color: #013582;
    color: white;
    font-size: 20px;
    font-weight: normal;
}

.e-dialog .e-dlg-header,
.e-dlg-header, .e-dlg-header * {
    color: white !important;
}

/* trucate text if text is exceeded three lines */

/* Set close button color white for Syncfuion Dialog*/
.e-dialog .e-btn .e-btn-icon.e-icon-dlg-close::before {
    color: white;
}

.e-grid td.e-active {
    background: #faa601 !important;
}

.validation-message {
    color: red;
    font-weight: bold;
    font-size: 10px;
    padding: 6px;
    background-color: #ffe9e9;

}

.approver-table
{
    width: 100%;
    border-collapse:collapse;
    border: 1px solid gray;
}
.approver-table thead th {
    background-color: #ccc;
    font-weight:600;
    padding:5px;
    text-align:center;
    border: 1px solid #bbb;
}

    .approver-table tbody td {
        padding: 5px;
        border: 1px solid #bbb;
        background-color:#fff;
    }


.sec-sep{
    height:1px;
    width: 90%;
    margin-left:auto;
    margin-right:auto;
    margin-top:40px;
    margin-bottom:10px;
    background-color:crimson;
}


.tmp-preview-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 30px;
    background: #fff;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    font-family:'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
    /*border-radius: 12px;*/
}

/* Header styles */
.tmp-preview-header {
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    position: relative;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.tmp-preview-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 95%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #013582, transparent);
}

.tmp-preview-main-title {
    color: #1a1a1a;
    font-size: 1.2rem;
    font-weight: 600;
}

.tmp-preview-meta-info {
    color: #6b7280;
    display: flex;
    font-size: 0.875rem;
    gap: 2rem;
    justify-content: center;
    margin-top: 0.75rem;
}

.tmp-preview-meta-item {
    display: inline-flex;
    align-items: center;
}

/* Details styles */
.tmp-preview-details-wrapper {
    background: #f9fafb;
    /*border: 1px solid #e5e7eb;*/
    /*border-radius: 0.5rem;*/
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.tmp-preview-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.tmp-preview-detail-block {
    padding: 0.75rem;
}

.tmp-preview-detail-label {
    color: #374151;
    font-weight: 600;
    margin-bottom: 0.375rem;
}

.tmp-preview-detail-value {
    color: #6b7280;
}

/* Section styles */
.tmp-preview-sections-wrapper {
    margin-top: 0.5rem;
}

.tmp-preview-sections-heading {
    color: #1a1a1a;
    font-size: 1.1rem;
    font-weight: 600;
    /* margin-bottom: 1.5rem; */
    text-align: center;
}

.tmp-preview-section-block {
    margin-bottom: 10px;
    /*border: 1px solid #eaeaea;*/
    /*border-radius: 8px;*/
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: transform 0.2s, box-shadow 0.2s;
}

.tmp-preview-section-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.tmp-preview-section-header {
    /*background: #f8f9fa;*/
    border-bottom: 1px solid #eaeaea;
    padding: 6px 10px;
    /*border-radius: 8px 8px 0 0;*/
}

.tmp-preview-section-title {
    color: #013582 !important;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    text-transform:capitalize;
}

.tmp-preview-required-tag {
    background: #fee2e2;
    /*border-radius: 0.25rem;*/
    color: #dc2626;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
}

.tmp-preview-section-content {
    color: #4b5563;
    font-size: 1rem;
    line-height: 1;
    padding: 10px;
}

    /* Rich text content styles */
    .tmp-preview-section-content h1,
    .tmp-preview-section-content h2,
    .tmp-preview-section-content h3 {
        color: #1f2937;
        margin: 1.5rem 0 0.75rem;
    }

    .tmp-preview-section-content p {
        margin-bottom: 1rem;
    }

    .tmp-preview-section-content ul,
    .tmp-preview-section-content ol {
        margin: 1rem 0;
        padding-left: 1.5rem;
    }

    .tmp-preview-section-content li {
        margin: 0.375rem 0;
    }

/* Footer styles */
.tmp-preview-footer {
    border-top: 1px solid #e5e7eb;
    color: #9ca3af;
    font-size: 0.875rem;
    margin-top: 3rem;
    padding-top: 1rem;
    text-align: center;
}

/* Table styles */
.approver-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.approver-table th {
    background: #f8f9fa;
    color: #013582;
    font-weight: 600;
    padding: 12px;
    text-align: left;
    border-bottom: 2px solid #eaeaea;
}

.approver-table td {
    padding: 12px;
    border-bottom: 1px solid #eaeaea;
    color: #444;
}

.approver-table tr:hover {
    background-color: #f8f9fa;
}

/* Memo header info */
.memo-header-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.memo-code {
    color: #6c757d;
    font-size: 0.9rem;
}

.memo-title {
    color: #013582;
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1.3;
}

.memo-type {
    color: #28a745;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Download link styling */
.mud-link {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    color: #013582;
    text-decoration: none;
    transition: color 0.2s;
}

.mud-link:hover {
    color: #0056b3;
}

/* Print-specific styles */
@media print {
    /* Hide non-printable elements */
    .no-print {
        display: none !important;
    }

    /* Reset container styles for print */
    .tmp-preview-container {
        max-width: none;
        margin: 0;
        padding: 10px;
        box-shadow: none;
        /*border-radius: 0;*/
    }

    /* Ensure white background */
    body {
        background: white;
    }

    /* Reset section styles for print */
    .tmp-preview-section-block {
        break-inside: avoid;
        page-break-inside: avoid;
        border: 1px solid #ddd;
        margin-bottom: 15px;
        box-shadow: none;
    }

    .tmp-preview-section-block:hover {
        transform: none;
        box-shadow: none;
    }

    /* Ensure tables break properly */
    .approver-table {
        break-inside: auto;
        page-break-inside: auto;
    }

    .approver-table tr {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    /* Optimize colors for print */
    .memo-title {
        color: black;
        font-size:16px;
    }

    .tmp-preview-section-title {
        color: black !important;
    }

    .approver-table th {
        background-color: #f0f0f0 !important;
        color: black;
    }

    /* Ensure links show their URLs */
    .mud-link[href]::after {
        content: " (" attr(href) ")";
        font-size: 0.9em;
        font-style: italic;
    }

    /* Optimize header for print */
    .tmp-preview-header {
        border-bottom: 2px solid #000;
    }

    .tmp-preview-header::after {
        display: none;
    }

    /* Add page breaks where needed */
    .page-block {
        page-break-before: always;
    }

    /* Ensure text is black for better printing */
    * {
        color: black !important;
    }

    /* Remove unnecessary backgrounds */
    .tmp-preview-section-header {
        background: none;
        border-bottom: 1px solid #000;
    }
}

.action-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.pdf-button {
    background-color: #d32f2f;
}

.pdf-button:hover {
    background-color: #b71c1c;
}

@media print {
    .action-buttons {
        display: none !important;
    }
}

/* Add these print-specific styles */
@media print {
    .tmp-preview-container {
        max-width: 100% !important;
        margin: 0 !important;
        padding: 10px !important;
        box-shadow: none !important;
    }

    table {
        font-size: 11px !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        page-break-inside: auto !important;
    }

    td, th {
        padding: 4px !important;
    }

    /* Force background colors for table headers */
    th {
        background-color: #f0f0f0 !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* Ensure proper page breaks */
    tr, td, th, p, div {
        page-break-inside: avoid !important;
    }

    /* Remove all shadows and effects */
    * {
        box-shadow: none !important;
        text-shadow: none !important;
    }
}


.corporate-watchlist-container {
    display: flex;
    height: calc(100vh - 105px);
    gap: 10px;
}

.left-pane {
    width: 300px;
    border-right: 1px solid #e0e0e0;
    background-color: var(--mud-palette-surface);
    position: relative;
    transition: width 0.3s ease;
}

    .left-pane.closed {
        width: 50px;
        overflow: hidden;
    }

.sidebar-toggle {
    position: absolute;
    top: 80px;
    right: -12px;
    width: 24px;
    height: 24px;
    background-color: var(--mud-palette-primary);
    color: var(--mud-palette-primary-text);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 100; /* Increased z-index to ensure visibility */
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Ensure the toggle button remains fully visible when sidebar is collapsed */
.left-pane.closed .sidebar-toggle {
    right: -24px; /* Move it completely outside the collapsed pane */
}

.right-pane {
    flex: 1;
    overflow: auto;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 6px;
    background: var(--mud-palette-surface);
    border-bottom: 1px solid var(--mud-palette-lines-default);
}

.project-list {
    height: calc(100vh - 155px);
    overflow-x: hidden;
    padding: 4px;
    overflow-y: auto;
}

.project-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 8px;
    margin: 2px 0;
    border-radius: 4px;
    transition: all 0.15s ease;
    cursor: pointer;
}

    .project-item:hover {
        background-color: var(--mud-palette-action-default-hover);
    }

    .project-item.selected {
        background-color: var(--mud-palette-primary-hover);
        color: var(--mud-palette-primary-text);
    }

.project-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

    .project-info .mud-icon-root {
        fill: rgb(1 53 130);
    }

    .project-info i.material-icons {
        color: var(--mud-palette-primary);
        font-size: 1.2rem;
    }

.project-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
}

.project-name {
    font-size: 0.813rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: black;
}

.project-code {
    font-size: 0.75rem;
    color: var(--mud-palette-text-secondary);
}

.badge {
    background-color: var(--mud-palette-error);
    color: white;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.688rem;
    padding: 0 4px;
    font-weight: 500;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stats {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
}

.stat-item {
    padding: 10px;
    text-align: center;
    min-width: 120px;
}

.no-selection {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #666;
}

/* Breadcrumb styles */
.breadcrumb {
    display: flex;
    padding: 8px 0;
}

    .breadcrumb ol {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .breadcrumb li {
        display: flex;
        align-items: center;
    }

        .breadcrumb li:not(:last-child)::after {
            content: "/";
            margin: 0 8px;
            color: var(--mud-palette-text-secondary);
        }

    .breadcrumb a {
        display: flex;
        align-items: center;
        color: var(--mud-palette-primary);
        text-decoration: none;
    }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

    .breadcrumb .active {
        color: var(--mud-palette-text-secondary);
    }

    .breadcrumb i {
        margin-right: 4px;
    }


.dashboard-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    padding: 16px;
    height: calc(100vh - 120px);
    overflow: auto;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e0e0e0;
}

.header-title {
    display: flex;
    align-items: center;
}

    .header-title h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 500;
        color: #333;
    }

.header-actions {
    display: flex;
    gap: 8px;
}

.refresh-button {
    color: #555;
    transition: all 0.2s ease;
}

    .refresh-button:hover {
        color: #0078d4;
        transform: rotate(15deg);
    }

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    padding: 10px 0;
}

.project-card {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 220px;
    border: 1px solid #e0e0e0;
}

    .project-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

.project-icon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 120px;
    position: relative;
}

.project-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.memo-color {
    background: linear-gradient(135deg, #2b88d8, #1a5a9c);
}

.ringi-color {
    background: linear-gradient(135deg, #c43e1c, #8c2a12);
}

.trs-color {
    background: linear-gradient(135deg, #107c41, #0a5a2d);
}

.default-color {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

.project-details {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(to bottom, #ffffff, #bfbebe);
    border-radius: 0 0 8px 8px;
}

.project-name {
    margin: 0 0 4px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.project-code {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 12px;
}

.approval-counts {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: auto;
}

.count-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    padding: 4px 0;
}

    .count-item.total {
        margin-top: 4px;
        padding-top: 8px;
        border-top: 1px dashed #e0e0e0;
        font-weight: 600;
    }

.count-label {
    color: #555;
}

.count-value {
    font-weight: 500;
    color: #333;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    grid-column: 1 / -1;
    padding: 40px;
}

    .loading-container span {
        color: #666;
        font-size: 0.9rem;
    }

.no-projects {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    grid-column: 1 / -1;
    padding: 40px;
    text-align: center;
    color: #666;
}

    .no-projects .mud-icon-root {
        color: #999;
        margin-bottom: 8px;
    }

/* Responsive adjustments */
@media (max-width: 768px) {
    .projects-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}

@media (max-width: 480px) {
    .projects-grid {
        grid-template-columns: 1fr;
    }
}


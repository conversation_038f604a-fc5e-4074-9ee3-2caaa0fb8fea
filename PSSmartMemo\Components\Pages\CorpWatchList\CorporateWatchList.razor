@page "/approval-list-not-in-use"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Syncfusion.Blazor.Grids
@inject CorporateService cService
@attribute [Authorize]
@rendermode InteractiveServer
@inject CorpApprovalDataService CorpAprService
@inject NavigationManager NavMgr
@inject WorklistDataService WorklistService



@* <SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Approval List" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb> *@

<div class="corporate-approval-container">
    <div class="project-panel @(sidebarOpen ? "open" : "closed")">
        <div class="panel-header">
            <div class="header-title">
                <MudIcon Icon="@Icons.Material.Filled.Folder" Size="Size.Small" Class="mr-2" />
                <span>Projects</span>
            </div>
            <div class="header-actions">
                <MudTooltip Text="Refresh Projects">
                    <MudIconButton Icon="@Icons.Material.Filled.Refresh"
                                  Size="Size.Small"
                                  OnClick="RefreshList"
                                  Class="refresh-button" />
                </MudTooltip>
            </div>
        </div>

        <div class="project-list">
            @foreach (var project in approvalsList)
            {
                <div class="project-item @(project == selectedProject ? "selected" : "")"
                     @onclick="() => SelectProject(project)">
                    <div class="project-icon @GetProjectIconClass(project.ProjectCode)">
                        @if (project.ProjectCode == "MMS")
                        {
                            <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Email" />
                        }
                        else if (project.ProjectCode == "RMS")
                        {
                            <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Description" />
                        }
                        else if (project.ProjectCode == "TRS")
                        {
                            <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Assignment" />
                        }
                        else
                        {
                            <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Folder" />
                        }
                    </div>
                    <div class="project-info">
                        <div class="project-name">@project.ProjectName</div>

                    </div>
                    @if (project.Total > 0)
                    {
                        <div class="badge">@project.Total</div>
                    }
                </div>
            }
        </div>
    </div>

    <div class="content-panel">
        @if (!string.IsNullOrEmpty(currentProjectCode))
        {
            <div class="content-header">
                <div class="header-left">
                    <MudIconButton 
                        Icon="@(sidebarOpen ? Icons.Material.Filled.ChevronLeft : Icons.Material.Filled.ChevronRight)" 
                        Size="Size.Small" 
                        OnClick="ToggleSidebar" 
                        Class="mr-2" />
                    <div class="project-title">
                        <div class="project-icon-sm @GetProjectIconClass(currentProjectCode)">
                            @if (currentProjectCode == "MMS")
                            {
                                <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Email" />
                            }
                            else if (currentProjectCode == "RMS")
                            {
                                <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Description" />
                            }
                            else if (currentProjectCode == "TRS")
                            {
                                <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Assignment" />
                            }
                        </div>
                        <h2>@currentProjectName</h2>
                    </div>
                </div>
                <div class="header-right">
                    <MudTooltip Text="Refresh Data">
                        <MudIconButton Icon="@Icons.Material.Filled.Refresh"
                                      Size="Size.Small"
                                      OnClick="@(() => RefreshProjectData())"
                                      Class="refresh-button" />
                    </MudTooltip>
                </div>
            </div>

            <div class="approval-tabs">
                @if (currentProjectCode == "MMS")
                {
                    <div class="memo-worklist-container">
                        <PSSmartMemo.Components.Pages.Worklist.MyWorklist />
                    </div>
                }
                else
                {
                    <MudTabs Elevation="0"
                             Rounded="true"
                             ApplyEffectsToContainer="true"
                             Class="approval-tabs-container"
                             @bind-ActivePanelIndex="activeTabIndex">
                        <MudTabPanel Text="Direct Approvals" Icon="@Icons.Material.Filled.Inbox">
                            @if (currentProjectCode == "RMS")
                            {
                                <SfGrid DataSource="RingiListDirect"
                                        AllowFiltering="true"
                                        AllowSorting="true"
                                        Height="calc(100vh - 160px)"
                                        AllowTextWrap="true"
                                        AllowSelection="true"
                                        AllowPaging="true"
                                        RowHeight="26"
                                        RowSelected="OnRowSelected"
                                        CssClass="compact-grid">
                                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                                    <GridPageSettings PageSize="20"></GridPageSettings>
                                    <GridEvents RowSelected="OnRowSelected" TValue="RINGIListItemDto"></GridEvents>
                                    <GridColumns>
                                        <GridColumn HeaderText="Reference" Width="160">
                                            <Template Context="context">
                                                @{
                                                    if (context is RINGIListItemDto item)
                                                    {
                                                        <div class="reference-cell">
                                                            <MudIcon Icon="@Icons.Material.Filled.Description" Color="Color.Error" Size="Size.Small" Class="reference-icon" />
                                                            <a class="reference-link" href="@item.previewUrl" target="_blank">@item.Reference</a>
                                                        </div>
                                                    }
                                                }
                                            </Template>
                                        </GridColumn>
                                        <GridColumn HeaderText="Control No" Field="@nameof(RINGIListItemDto.ControlNo)" Width="120"></GridColumn>
                                        <GridColumn HeaderText="Start Date" Field="@nameof(RINGIListItemDto.StartDate)" Width="100"></GridColumn>
                                        <GridColumn HeaderText="Request Type" Field="@nameof(RINGIListItemDto.RequestType)" Width="130"></GridColumn>
                                        <GridColumn HeaderText="Forward By" Field="@nameof(RINGIListItemDto.ForwardBy)" Width="130"></GridColumn>
                                        <GridColumn HeaderText="Status" Field="@nameof(RINGIListItemDto.Status)" Width="100">
                                            <Template Context="context">
                                                @{
                                                    if (context is RINGIListItemDto item)
                                                    {
                                                        <MudChip T="string" Size="Size.Small"
                                                                 Color="@GetStatusColor(item.Status)">
                                                            @item.Status
                                                        </MudChip>
                                                    }
                                                }
                                            </Template>
                                        </GridColumn>
                                        <GridColumn HeaderText="Pending Since" Field="@nameof(RINGIListItemDto.PendingSince)" Width="120"></GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            }
                        </MudTabPanel>
                        <MudTabPanel Text="Indirect Approvals" Icon="@Icons.Material.Filled.ForwardToInbox">
                            @if (currentProjectCode == "RMS")
                            {
                                <SfGrid DataSource="RingiListIndirect"
                                        AllowFiltering="true"
                                        AllowSorting="true"
                                        Height="calc(100vh - 160px)"
                                        AllowTextWrap="true"
                                        AllowSelection="true"
                                        AllowPaging="true"
                                        RowHeight="26"
                                        RowSelected="OnRowSelected"
                                        CssClass="compact-grid">
                                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                                    <GridPageSettings PageSize="20"></GridPageSettings>
                                    <GridEvents RowSelected="OnRowSelected" TValue="RINGIListItemDto"></GridEvents>
                                    <GridColumns>
                                        <GridColumn HeaderText="Reference" Width="160">
                                            <Template Context="context">
                                                @{
                                                    if (context is RINGIListItemDto item)
                                                    {
                                                        <div class="reference-cell">
                                                            <MudIcon Icon="@Icons.Material.Filled.Description" Color="Color.Info" Size="Size.Small" Class="reference-icon" />
                                                            <a class="reference-link" href="@item.previewUrl" target="_blank">@item.Reference</a>
                                                        </div>
                                                    }
                                                }
                                            </Template>
                                        </GridColumn>
                                        <GridColumn HeaderText="Control No" Field="@nameof(RINGIListItemDto.ControlNo)" Width="120"></GridColumn>
                                        <GridColumn HeaderText="Start Date" Field="@nameof(RINGIListItemDto.StartDate)" Width="100"></GridColumn>
                                        <GridColumn HeaderText="Request Type" Field="@nameof(RINGIListItemDto.RequestType)" Width="130"></GridColumn>
                                        <GridColumn HeaderText="Forward By" Field="@nameof(RINGIListItemDto.ForwardBy)" Width="130"></GridColumn>
                                        <GridColumn HeaderText="Status" Field="@nameof(RINGIListItemDto.Status)" Width="100">
                                            <Template Context="context">
                                                @{
                                                    if (context is RINGIListItemDto item)
                                                    {
                                                        <MudChip T="string" Size="Size.Small"
                                                                 Color="@GetStatusColor(item.Status)">
                                                            @item.Status
                                                        </MudChip>
                                                    }
                                                }
                                            </Template>
                                        </GridColumn>
                                        <GridColumn HeaderText="Pending Since" Field="@nameof(RINGIListItemDto.PendingSince)" Width="120"></GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            }
                        </MudTabPanel>
                    </MudTabs>
                }
            </div>
        }
        else
        {
            <div class="empty-state">
                <div class="empty-state-content">
                    <MudIcon Icon="@Icons.Material.Filled.Inbox" Size="Size.Large" Class="empty-state-icon" />
                    <h3>Select a project</h3>
                    <p>Choose a project from the list to view pending approvals</p>
                </div>
            </div>
        }
    </div>
</div>


@code {
    private List<PendingApprovalDto> approvalsList = new();
    private PendingApprovalDto? selectedProject;
    private bool sidebarOpen = true;
    private string userId = "";
    private int activeTabIndex = 0;
    private DynamicApprovalGrid? dynamicGrid;
    private List<RINGIListItemDto> RingiListDirect = new();
    private List<RINGIListItemDto> RingiListIndirect = new();
    private string currentProjectCode = "";
    private string currentProjectName = "";
    private List<MemoDto> memoWorklist = new();

    [CascadingParameter] private Task<AuthenticationState>? AuthState { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
            }
        }

        if (string.IsNullOrEmpty(userId))
        {
            NavMgr.NavigateTo("/");
        }

        await RefreshList();
    }

    private async Task SelectProject(PendingApprovalDto project)
    {
        currentProjectCode = "";
        currentProjectName = "";
        selectedProject = project;
        activeTabIndex = 0; // Reset to first tab when switching projects

        if (project.ProjectCode == "RMS")
        {
            currentProjectCode = project.ProjectCode;
            currentProjectName = project.ProjectName;
            await RefreshProjectData();
        }
        else if (project.ProjectCode == "MMS")
        {
            currentProjectCode = project.ProjectCode;
            currentProjectName = "Memo";
            await RefreshProjectData();
        }
        else if (project.ProjectCode == "TRS")
        {
            currentProjectCode = project.ProjectCode;
            currentProjectName = project.ProjectName;
        }
    }

    private async Task RefreshProjectData()
    {
        if (currentProjectCode == "RMS")
        {
            RingiListDirect = await CorpAprService.GetRINGIListAsync(userId, 1);
            RingiListIndirect = await CorpAprService.GetRINGIListAsync(userId, 2);
            StateHasChanged();
        }
        else if (currentProjectCode == "MMS")
        {
            // Memo data is loaded directly by the MyWorklist component
            StateHasChanged();
        }
    }

    private void ToggleSidebar()
    {
        sidebarOpen = !sidebarOpen;
        StateHasChanged();
    }

    private async Task RefreshList()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                var userId = authState.User.Identity.Name!;
                approvalsList = await cService.GetEmpPendingApprovals(userId);

                // Add Memo project if it doesn't exist
                if (!approvalsList.Any(p => p.ProjectCode == "MMS"))
                {
                    approvalsList.Add(new PendingApprovalDto
                    {
                        ProjectCode = "MMS",
                        ProjectName = "Memo",
                        Direct = 0,
                        Indirect = 0,
                        Total = 0,
                        Link = "/worklist"
                    });
                }

                approvalsList = approvalsList.OrderBy(x => x.ProjectCode).ToList();
                StateHasChanged();
            }
        }
    }

    private string GetProjectIconClass(string projectCode)
    {
        return projectCode switch
        {
            "MMS" => "memo",
            "RMS" => "ringi",
            "TRS" => "trs",
            _ => ""
        };
    }

    private void OpenItemPreview(RINGIListItemDto item)
    {
        if (!string.IsNullOrEmpty(item.previewUrl))
        {
            NavMgr.NavigateTo(item.previewUrl, true);
        }
    }

    private Color GetStatusColor(string status)
    {
        return status?.ToLower() switch
        {
            "pending" => Color.Warning,
            "approved" => Color.Success,
            "rejected" => Color.Error,
            "completed" => Color.Info,
            _ => Color.Default
        };
    }

    private void OnRowSelected(RowSelectEventArgs<RINGIListItemDto> args)
    {
        if (args.Data != null && !string.IsNullOrEmpty(args.Data.previewUrl))
        {
            NavMgr.NavigateTo(args.Data.previewUrl, true);
        }
    }
}





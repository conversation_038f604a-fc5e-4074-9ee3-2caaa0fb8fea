namespace PSSmartMemo.DTO;

public class MemoAttachmentDto
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int? AttachmentTypeId { get; set; }
    public string? AttachmentType { get; set; }
    public int MemoId { get; set; }
    public string TempId { get; set; }
}
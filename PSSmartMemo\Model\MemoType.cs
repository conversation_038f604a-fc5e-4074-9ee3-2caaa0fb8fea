﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class MemoType
{
    public int MemoTypeId { get; set; }

    public string MemoTypeCode { get; set; }

    public string MemoTypeName { get; set; }

    public DateTime? MemoTypeCreatedDate { get; set; }

    public string MemoTypeCreatedBy { get; set; }

    public DateTime? MemoTypeModifiedDate { get; set; }

    public string MemoTypeModifiedBy { get; set; }

    public bool? MemoTypeIsActive { get; set; }

    public bool? MemoTypeIsDel { get; set; }

    public virtual ICollection<AttachmentType> AttachmentTypes { get; set; } = new List<AttachmentType>();

    public virtual ICollection<MemoTemplate> MemoTemplates { get; set; } = new List<MemoTemplate>();

    public virtual ICollection<Memo> Memos { get; set; } = new List<Memo>();
}
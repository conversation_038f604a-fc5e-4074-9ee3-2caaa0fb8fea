namespace PSSmartMemo.DTO;

public class MemoDetailDto
{
    public int MemoId { get; set; }
    public string? Title { get; set; }
    public int? MemoTemplateId { get; set; }
    public string? Template { get; set; }
    public int? MaxApprover { get; set; }

    public List<MemoSectionDto> Sections { get; set; } = new();
    public List<MemoAttachmentDto> Attachments { get; set; } = new();
    public List<MemoApproverDto> ApproverList { get; set; } = new();
}
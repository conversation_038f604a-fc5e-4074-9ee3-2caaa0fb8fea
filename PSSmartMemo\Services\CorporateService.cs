﻿using System.Data;
using System.Net;
using System.Net.Http.Headers;
using System.Web;
using System.Xml;
using System.Diagnostics;
using static System.Net.WebRequestMethods;
using Newtonsoft.Json;


namespace PSSmartMemo.Services;
public class PinValidationResponse
{
    public string pCode { get; set; } = ""; // Initialize to avoid null reference exceptions
}
public class CorporateService(HttpClient http, IDbContextFactory<ApplicationDbContext> contextFactory, IConfiguration configuration)
{
    // paste here
    public string EmpNoUpdate(string EmpNo)
    {
        if (EmpNo == @"KHI-SOFT-056\Jawaid" || EmpNo == @"NABIL-PC\Administrator") return EmpNo;
        EmpNo = EmpNo.ToLower().Replace("psmcl\\pkb0", "");

        if (long.Parse(EmpNo) >= 1 && long.Parse(EmpNo) <= 1000)
        {
            if (EmpNo.Length == 1) EmpNo = "0010000" + EmpNo;
            else if (EmpNo.Length == 2) EmpNo = "001000" + EmpNo;
            else if (EmpNo.Length == 3) EmpNo = "00100" + EmpNo;
            else if (EmpNo.Length == 4) EmpNo = "0010" + EmpNo;
            else if (EmpNo.Length == 5) EmpNo = "001" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 20001 && long.Parse(EmpNo) <= 21000)
        {
            EmpNo = "001" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 21001 && long.Parse(EmpNo) <= 29999)
        {
            EmpNo = "005" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 30001 && long.Parse(EmpNo) <= 39999)
        {
            EmpNo = "001" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 50001 && long.Parse(EmpNo) <= 69999)
        {
            EmpNo = "003" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 70001 && long.Parse(EmpNo) <= 79999)
        {
            EmpNo = "003" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 80001 && long.Parse(EmpNo) <= 89999)
        {
            EmpNo = "004" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 95001 && long.Parse(EmpNo) <= 99999)
        {
            EmpNo = "006" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 1001 && long.Parse(EmpNo) <= 9999)
        {
            EmpNo = "008" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 10001 && long.Parse(EmpNo) <= 19999)
        {
            EmpNo = "008" + EmpNo;
        }

        return EmpNo;
    }

    public string GetCodeOnly(string code)
    {
        if (code == null) return "";

        if (code == @"KHI-SOFT-056\Jawaid" || code == @"NABIL-PC\Administrator")
            return "00420";

        if (code.Length < 5) return code;

        return code.Substring(code.Length - 5);
    }

    public DataTable GetEmployeeData2(string StrGetDate)
    {
        var dtSAP = new DataTable();
        dtSAP.Columns.Add("empcode", typeof(string));
        dtSAP.Columns.Add("FullName", typeof(string));
        dtSAP.Columns.Add("JobTitle", typeof(string));
        dtSAP.Columns.Add("location_code", typeof(string));
        dtSAP.Columns.Add("location_text", typeof(string));
        dtSAP.Columns.Add("employee_shift_code", typeof(string));
        dtSAP.Columns.Add("employee_official_email", typeof(string));
        dtSAP.Columns.Add("department_name", typeof(string));
        dtSAP.Columns.Add("tooltip", typeof(string));

        var dtrow = dtSAP.NewRow();

        dtrow["empcode"] = "123";
        dtrow["FullName"] = "Jawaid Akhter";
        dtrow["JobTitle"] = "Manager IT";
        dtrow["location_code"] = "L1";
        dtrow["location_text"] = "Head Office-AMD";
        dtrow["employee_shift_code"] = "S1";
        dtrow["employee_official_email"] = "<EMAIL>";
        dtrow["tooltip"] = "Jawaid Akhter";

        dtSAP.Rows.Add(dtrow);
        return dtSAP;
    }

    public DataTable GetEmployeeData(string empCode)
    {
        var dtSAP = new DataTable();

        try
        {
            if (empCode == @"KHI-SOFT-056\Jawaid" || empCode == @"NABIL-PC\Administrator")
                return GetEmployeeData2(empCode);


            //string  StrMainEmpCode = "00130062

            //string  StrMainEmpCode = empCode.Substring(11,5);
            //330
            // 
            //empCode = "00130062";
            empCode = EmpNoUpdate(empCode);
            //string sapApiUrl = "http://ps2-pop-ci.stan.suzuki:50000/igwj/odata/SAP/ZGET_CIPEMPINFO_CDS/ZGET_CIPEMPINFO(Pempcode='{0}')/Set?j_username=POUSER&j_password=Psmc@12345";
            var sapApiUrl = configuration.GetSection("sapapiurl").Value ?? "";
            var strUrl = string.Format(sapApiUrl, empCode);


            var request1 = WebRequest.Create(strUrl);
            request1.Method = Http.Get;
            request1.ContentType = "applications/xml; charset=uft-8";
            var respose1 = request1.GetResponse();

            if (((HttpWebResponse)respose1).StatusCode == HttpStatusCode.OK)
            {
                var ReceiveStream = respose1.GetResponseStream();
                var reader = new StreamReader(ReceiveStream);
                var resposeFromServer = reader.ReadToEnd();

                var xmlDoc = new XmlDocument();
                xmlDoc.LoadXml(resposeFromServer);
                var parentNode = xmlDoc.GetElementsByTagName("m:properties");

                dtSAP.Columns.Add("empcode", typeof(string));
                dtSAP.Columns.Add("FullName", typeof(string));
                dtSAP.Columns.Add("JobTitle", typeof(string));
                dtSAP.Columns.Add("location_code", typeof(string));
                dtSAP.Columns.Add("location_text", typeof(string));
                dtSAP.Columns.Add("employee_shift_code", typeof(string));
                dtSAP.Columns.Add("employee_official_email", typeof(string));
                dtSAP.Columns.Add("department_name", typeof(string));
                dtSAP.Columns.Add("tooltip", typeof(string));


                foreach (XmlNode childrenNode in parentNode)
                {
                    var dtrow = dtSAP.NewRow();
                    foreach (XmlNode ChildNode in childrenNode)
                    {
                        //StrDOB = childrenNode["d:DOB1"].InnerText;
                        
                            
                        dtrow["empcode"] = childrenNode["d:empcode"]!.InnerText;
                        dtrow["FullName"] = childrenNode["d:FullName"]!.InnerText;
                        dtrow["JobTitle"] = childrenNode["d:JobTitle"]!.InnerText;
                        dtrow["location_code"] = childrenNode["d:location_code"]!.InnerText;
                        dtrow["location_text"] = childrenNode["d:location_text"]!.InnerText;
                        dtrow["employee_shift_code"] = childrenNode["d:employee_shift_code"]!.InnerText;
                        dtrow["employee_official_email"] = childrenNode["d:employee_official_email"]!.InnerText;
                        dtrow["tooltip"] = childrenNode["d:FullName"]!.InnerText;
                        try
                        {
                            dtrow["department_name"] = childrenNode["d:department_name"]!.InnerText;
                        }
                        catch (Exception)
                        {
                            dtrow["department_name"] = "";
                        }
                    }

                    dtSAP.Rows.Add(dtrow);
                    //StrDOB = "";
                }
            }

            return dtSAP;
        }
        catch (Exception ex)
        {
            // in case if not able to access odata service then return empty datatable
            dtSAP.Columns.Add("empcode", typeof(string));
            dtSAP.Columns.Add("FullName", typeof(string));
            dtSAP.Columns.Add("JobTitle", typeof(string));
            dtSAP.Columns.Add("location_code", typeof(string));
            dtSAP.Columns.Add("location_text", typeof(string));
            dtSAP.Columns.Add("employee_shift_code", typeof(string));
            dtSAP.Columns.Add("employee_official_email", typeof(string));
            dtSAP.Columns.Add("department_name", typeof(string));
            dtSAP.Columns.Add("tooltip", typeof(string));
            // set fullname as "Unable to Get Employee Data"
            var dtrow = dtSAP.NewRow();
            dtrow["empcode"] = "0";
            dtrow["FullName"] = "Unable to Get Employee Data: ";
            dtrow["JobTitle"] = "Unable to Get Employee Data";
            dtrow["location_code"] = "Unable to Get Employee Data";
            dtrow["location_text"] = "Unable to Get Employee Data";
            dtrow["employee_shift_code"] = "Unable to Get Employee Data";
            dtrow["employee_official_email"] = "Unable to Get Employee Data";
            dtrow["department_name"] = "Unable to Get Employee Data";
            dtrow["tooltip"] = ex.Message;
            if (ex.InnerException != null)
                dtrow["tooltip"] += " - Detail: " + ex.InnerException.Message;
            dtSAP.Rows.Add(dtrow);

            return dtSAP;
        }
    }

    //public async Task<string> UpdateUsers(List<Employee2Dto> allEmps)
    //{
    //    var dc = contextFactory.CreateDbContext();
    //    var tran = dc.Database.BeginTransaction();
    //    try
    //    {
    //        foreach (var emp in allEmps)
    //        {
    //            var q = (from a in dc.Users
    //                where a.UserId.ToLower() == ("psmcl\\PKB0" + emp.EmpCode).ToLower()
    //                select a).FirstOrDefault();
    //            if (q == null)
    //            {
    //                // create new user
    //                var q2 = new User
    //                {
    //                    UserId = "psmcl\\PKB0" + emp.EmpCode.ToLower(),
    //                    Name = emp.FullName,
    //                    Email = emp.EmployeeOfficialEmail.ToLower(),
    //                    Code = emp.EmpCode.ToLower(),
    //                    CreatedBy = 0,
    //                    CreatedDate = DateTime.Now,
    //                    EmployeeCode = emp.EmpCode,
    //                    MobileNumber = "+923000000000",
    //                    Password = "***123***",
    //                    IsActive = emp.Status == "3",
    //                    IsAdmin = false
    //                };
    //                dc.Users.Add(q2);
    //                await dc.SaveChangesAsync();
    //            }
    //            else
    //            {
    //                q.IsActive = emp.Status == "3";
    //                await dc.SaveChangesAsync();
    //            }
    //        }

    //        await tran.CommitAsync();
    //        return await Task.FromResult("OK");
    //    }
    //    catch (Exception ex)
    //    {
    //        await tran.RollbackAsync();
    //        return await Task.FromResult($"Error updating users: {ex.Message}");
    //    }
    //}

    public async Task<string> UpdateUsers(List<Employee2Dto> allEmps)
    {
        var dc = contextFactory.CreateDbContext();
        var tran = dc.Database.BeginTransaction();
        try
        {
            foreach (var emp in allEmps)
            {
                var q = (from a in dc.Users
                         where a.UserId.ToLower() == ("psmcl\\PKB0" + emp.EmpCode).ToLower()
                         select a).FirstOrDefault();
                if (q == null)
                {
                    // create new user
                    var q2 = new User
                    {
                        UserId = "psmcl\\PKB0" + emp.EmpCode.ToLower(),
                        Name = emp.FullName,
                        Email = emp.EmployeeOfficialEmail.ToLower(),
                        Code = emp.EmpCode.ToLower(),
                        CreatedBy = 0,
                        CreatedDate = DateTime.Now,
                        EmployeeCode = emp.EmpCode,
                        MobileNumber = "+923000000000",
                        Password = "***123***",
                        IsActive = emp.Status == "3",
                        IsAdmin = false
                    };
                    dc.Users.Add(q2);
                    await dc.SaveChangesAsync();
                }
                else
                {
                    q.IsActive = emp.Status == "3";
                    q.Name = emp.FullName;
                    q.Email = emp.EmployeeOfficialEmail.ToLower();
                    q.EmployeeCode = emp.EmpCode;
                    q.ModifiedBy = 0;
                    q.ModifiedDate = DateTime.Now;
                    q.MobileNumber = "+923000000000"; // Default mobile number, can be updated later
                    q.Password = "123";
                    //q.Password = "***123***"; // Default password, can be updated later
                    q.UserId = "psmcl\\PKB0" + emp.EmpCode.ToLower();
                    await dc.SaveChangesAsync();

                }
            }

            await tran.CommitAsync();
            return await Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            await tran.RollbackAsync();
            return await Task.FromResult($"Error updating users: {ex.Message}");
        }
    }




    public Task<List<Employee2Dto>> ExcludeExists(List<Employee2Dto> currentEmployees)
    {
        var dc = contextFactory.CreateDbContext();
        var allEmployees = dc.Users.ToList();

        // Initialize the list to hold both mismatched and new employees
        var mismatchedEmployees = new List<Employee2Dto>();

        // 1. Find existing employees with mismatched data
        var existingMismatched = (from emp in currentEmployees
                                  where allEmployees.Any(a => a.UserId.ToLower() == ("psmcl\\PKB0" + emp.EmpCode).ToLower()
                                    && (a.Name != emp.FullName
                                    || a.Email != emp.EmployeeOfficialEmail.ToLower()
                                    || a.EmployeeCode != emp.EmpCode
                                    || a.IsActive != (emp.Status == "3")))
                                  select emp).ToList();

        mismatchedEmployees.AddRange(existingMismatched);

        // 2. Find new employees (employees in currentEmployees that do not exist in allEmployees)
        var newEmployees = (from emp in currentEmployees
                            where !allEmployees.Any(a => a.UserId.ToLower() == ("psmcl\\PKB0" + emp.EmpCode).ToLower())
                            select emp).ToList();

        mismatchedEmployees.AddRange(newEmployees);

        return Task.FromResult(mismatchedEmployees);
    }

    public async Task<List<Employee2Dto>> GetAllEmployeeData()
    {
        try
        {
            var sapApiUrl = configuration.GetSection("sapapiurlAllEmp").Value ?? "";
            List<Employee2Dto> allEmp = new();

            using var client = new HttpClient();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/xml"));
            var response = await client.GetAsync(sapApiUrl);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var xmlDoc = new XmlDocument();
                xmlDoc.LoadXml(responseContent);

                var parentNode = xmlDoc.GetElementsByTagName("m:properties");
                foreach (XmlNode childrenNode in parentNode)
                {
                    var emp = new Employee2Dto
                    {
                        EmployeeNo = GetNodeValue(childrenNode, "d:employee_no"),
                        EmpCode = GetNodeValue(childrenNode, "d:empcode"),
                        FullName = GetNodeValue(childrenNode, "d:FullName"),
                        JobTitle = GetNodeValue(childrenNode, "d:JobTitle"),
                        LocationCode = GetNodeValue(childrenNode, "d:location_code"),
                        LocationText = GetNodeValue(childrenNode, "d:location_text"),
                        EmployeeShiftCode = GetNodeValue(childrenNode, "d:employee_shift_code"),
                        EmployeeOfficialEmail = GetNodeValue(childrenNode, "d:employee_official_email"),
                        DepartmentName = GetNodeValue(childrenNode, "d:department_name"),
                        PosText = GetNodeValue(childrenNode, "d:pos_text"),
                        Status = GetNodeValue(childrenNode, "d:status")
                    };

                    allEmp.Add(emp);
                }
            }

            return allEmp;
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"An error occurred: {ex.Message}");
            return new List<Employee2Dto>();
        }
    }


    /*
        public Task<List<MedicalOPDExpDto>> GetMedicalOPDExp2(string code)
        {
            var op = "[  {    \"entitlelimit\": 145000,    \"expenseAmount\": 58428,    \"usagepercentage\": 44.3  }]";
            // Deserialize the JSON array into a List<MedicalOPDExpDto>
            var op2 = JsonConvert.DeserializeObject<List<MedicalOPDExpDto>>(op);

            return Task.FromResult(op2);
        }
    */


    // Get Location and Shift information
    public Task<(string, string)> GetShiftAndLocationInfo(string locationCode, string shiftCode)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.ShiftLocations
            where a.LocationCode == locationCode &&
                  a.EmployeeShiftCode == shiftCode
            select new
            {
                a.LocationCode,
                a.LocationTxt,
                a.ShiftStart,
                a.ShiftEnd
            }).FirstOrDefault();
        if (q == null) return Task.FromResult(("Location Not Mapped", "Shift Not Mapped"));

        return Task.FromResult((q.LocationTxt, q.ShiftStart + " - " + q.ShiftEnd));
    }

    // write a function that will convert a time string into proper time format, for example 171500 => will be 5:15 PM
    public static string ConvertTime(double input)
    {
        var timeString = input.ToString("000000");

        var hours = int.Parse(timeString.Substring(0, 2));
        var minutes = int.Parse(timeString.Substring(2, 2));
        var seconds = int.Parse(timeString.Substring(4, 2));

        var amPm = hours >= 12 ? "PM" : "AM";
        hours = hours % 12;
        if (hours == 0) hours = 12;

        return $"{hours}:{minutes:D2} {amPm}";
    }

    public Task<List<LocationDto>> GetAllShiftsAndLocations()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.ShiftLocations
            select new LocationDto
            {
                Id = a.Id,
                ShiftEnd = a.ShiftEnd,
                ShiftStart = a.ShiftStart,
                LocationCode = a.LocationCode,
                LocationTxt = a.LocationTxt,
                EmployeeShiftCode = a.EmployeeShiftCode
            }).ToList();
        return Task.FromResult(q);
    }

    public static string ConvertTimeDouble(double input)
    {
        var timeString = input.ToString("000000");

        var hours = int.Parse(timeString.Substring(0, 2));
        var minutes = int.Parse(timeString.Substring(2, 2));
        var seconds = int.Parse(timeString.Substring(4, 2));

        return $"{hours:D2}:{minutes:D2}:{seconds:D2}";
    }

    public async Task<List<EmpContactInfo>> GetEmployeeContacts(string searchText, string division, string department)
    {
        searchText = (searchText ?? "").Trim();
        division = (division ?? "").Trim();
        department = (department ?? "").Trim();

        if (string.IsNullOrEmpty(searchText)) searchText = "%20";
        if (string.IsNullOrEmpty(division)) division = "%20";
        if (string.IsNullOrEmpty(department)) department = "%20";

        try
        {
            //var encodedSearchText = HttpUtility.UrlEncode(searchText);
            //var encodedDivision = HttpUtility.UrlEncode(division);
            //var encodedDepartment = HttpUtility.UrlEncode(department);

            var apibaseurl = configuration.GetSection("empsearchurl").Value;

            var url = $"{apibaseurl}EmpContactInformation/{searchText}/{division}/{department}";

            var response = await http.GetFromJsonAsync<List<EmpContactInfo>>(url);
            return response ?? new List<EmpContactInfo>();
        }
        catch (HttpRequestException)
        {
            // Handle or log the exception as needed
            return new List<EmpContactInfo>();
        }
    }

    public async Task<List<PendingApprovalDto>> GetEmpPendingApprovals(string code)
    {
        var url = "";
        var actualCode = code;
        try
        {
            code = GetCodeOnly(code);
            code = HttpUtility.UrlEncode(code);

            var apibaseurl = configuration.GetSection("empsearchurl").Value;
            url = $"{apibaseurl}PendingApprovalInformation/{code}";

            var response = await http.GetFromJsonAsync<List<PendingApprovalDto>>(url);
            response = response
                .Where(c => c.ProjectName!="Feedback" && c.ProjectName!= "Promortion Ability Assessment")
                .OrderBy(c=>c.ProjectName)
                .ToList();
            
            // Add memo approvals count from database
            var dc = contextFactory.CreateDbContext();
            //var userId = "psmcl\\PKB0" + code;
            var memoCount = dc.MemoApprovalLogs
                .Count(a => a.ToApprover.MemoApproverUserId.ToLower() == actualCode.ToLower() && a.ReplyLogId == null);
            
            // Add memo project if it doesn't exist in the response
            if (!response.Any(p => p.ProjectCode == "MMS"))
            {
                response.Add(new PendingApprovalDto
                {
                    ProjectCode = "MMS",
                    ProjectName = "Memo",
                    Direct = memoCount,
                    Indirect = 0,
                    Total = memoCount,
                    Link = "/memo-approvals"
                });
            }
            else
            {
                // Update existing memo project with count
                var memoProject = response.FirstOrDefault(p => p.ProjectCode == "MMS");
                if (memoProject != null)
                {
                    memoProject.Direct = memoCount;
                    memoProject.Total = memoCount;
                }
            }
            
            return response.Where(c=>c.Total>0).OrderBy(c=>c.ProjectName).ToList() ?? new List<PendingApprovalDto>();
        }
        catch (Exception ex)
        {
            // Handle or log the exception as needed
            var msg = ex.Message;
            if (ex.InnerException != null)
                msg += " - Detail: " + ex.InnerException.Message;
            msg += " - URL: " + url;
            var pa = new PendingApprovalDto
                { Direct = 1, Indirect = 1, Link = "Error", ProjectCode = "Error", ProjectName = msg, Total = 1 };
            var lsg = new List<PendingApprovalDto> { pa };
            return lsg;
        }
    }


    public Task<string> DownloadMyJD(string empcode, string folderPath)
    {
        try
        {
            var baseUrl = configuration.GetSection("apibaseurl").Value;
            empcode = GetCodeOnly(empcode);

            var response = http.GetAsync($"{baseUrl}api/Pdf/generate-pdf/{empcode}").Result;

            if (response.IsSuccessStatusCode)
            {
                var fileName = response.Content.Headers.ContentDisposition?.FileName ?? $"{empcode}_document.pdf";
                var filePath = Path.Combine(folderPath, fileName);

                using (var stream = response.Content.ReadAsStreamAsync().Result)
                using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None))
                {
                    stream.CopyToAsync(fileStream).Wait();
                }

                return Task.FromResult("/employeejd/" + fileName);
            }

            return Task.FromResult(
                $"Failed to download PDF for employee code {empcode}. Status code: {response.StatusCode}");
        }
        catch (Exception)
        {
            return Task.FromResult("");
        }
    }


    private XmlNamespaceManager GetNamespaceManager(XmlDocument xmlDoc)
    {
        var nsManager = new XmlNamespaceManager(xmlDoc.NameTable);
        nsManager.AddNamespace("m", "http://schemas.microsoft.com/ado/2007/08/dataservices/metadata");
        nsManager.AddNamespace("d", "http://schemas.microsoft.com/ado/2007/08/dataservices");
        return nsManager;
    }

    private string GetNodeValue(XmlNode parentNode, string nodeName)
    {
        var node = parentNode[nodeName];
        return node != null ? node.InnerText : string.Empty;
    }


    private string GetRequiredNodeValue(XmlNode parentNode, string nodeName)
    {
        var node = parentNode.SelectSingleNode(nodeName);
        if (node == null) throw new FormatException($"Required node '{nodeName}' not found.");
        return node.InnerText;
    }
    
    public async Task<bool> ValidatePinAsync(string userId, string password)
    {
        userId = GetCodeOnly(userId);

        try
        {
            // 1. URL Encoding:
            var apibaseurl = configuration.GetSection("apibaseurl").Value;
            string encodedCredentials = $"{Uri.EscapeDataString(userId)},{Uri.EscapeDataString(password)}";
            string apiUrl = $"{apibaseurl}PCode/{encodedCredentials}";

            // 2. Make the API call:
            using HttpResponseMessage response = await http.GetAsync(apiUrl);
            if (response.IsSuccessStatusCode)
            {
                // 3. Deserialize the JSON response directly into the DTO:
                
                var res2 = await response.Content.ReadFromJsonAsync<List<PinValidationResponse>>();
                //PinValidationResponse? result = await response.Content.ReadFromJsonAsync<PinValidationResponse>();
                if(res2!=null && res2.Any() && res2[0].pCode=="1")
                {
                    return true;
                }
                return false;

                //return result is { pCode: "1" };
                //Console.WriteLine("Invalid JSON response (null).");
            }

            Console.WriteLine($"API request failed with status code: {response.StatusCode}");
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An error occurred: {ex.Message}");
            return false;
        }
    }


    // Get All Divisions by using httpclient, endpoint will be /divisions and cast data into List<DivisionDto>, order by name
    public Task<List<DivisionDto>> GetDivisions()
    {
        var baseUrl = configuration.GetSection("empsearchurl").Value;

        try
        {
            var url = $"{baseUrl}division";
            var divisions = http.GetFromJsonAsync<List<DivisionDto>>(url).Result ?? new List<DivisionDto>();
            divisions.Insert(0, new DivisionDto() { Code = " ", Name = " - Any Division -" });
            return Task.FromResult(divisions.OrderBy(d => d.Name).ToList());
        }
        catch (HttpRequestException e)
        {
            Console.WriteLine($"HTTP request error: {e.Message}");
            var msg = e.Message;
            var ai = new List<DivisionDto> { new() { Code = "Error", Name = msg } };
            return Task.FromResult(ai);
        }
        catch (JsonException e)
        {
            Console.WriteLine($"JSON parsing error: {e.Message}");
            var msg = e.Message;
            var ai = new List<DivisionDto> { new() { Code = "Error", Name = msg } };
            return Task.FromResult(ai);
        }
        catch (Exception e)
        {
            Console.WriteLine($"An error occurred: {e.Message}");
            var msg = e.Message;
            var ai = new List<DivisionDto> { new() { Code = "Error", Name = msg } };
            return Task.FromResult(ai);
        }
    }


    public Task<List<DepartmentDto>> GetDepartments(string divisioncode)
    {
        var baseUrl = configuration.GetSection("empsearchurl").Value;
        string msg = "";

        try
        {
            var url = $"{baseUrl}DepartmentInfo/{divisioncode}";
            var divisions = http.GetFromJsonAsync<List<DepartmentDto>>(url).Result;

            return Task.FromResult(divisions.OrderBy(d => d.Name).ToList());
        }
        catch (HttpRequestException e)
        {
            Console.WriteLine($"HTTP request error: {e.Message}");
            msg = e.Message;
        }
        catch (JsonException e)
        {
            Console.WriteLine($"JSON parsing error: {e.Message}");
            msg = e.Message;
        }
        catch (Exception e)
        {
            Console.WriteLine($"An error occurred: {e.Message}");
            msg = e.Message;
        }

        var dpt = new DepartmentDto() { Code = "", Name = msg };
        var res = new List<DepartmentDto> { dpt };
        return Task.FromResult(res);

    }

    public async Task<string> SendEmailAsync(string emailAddress, string code, string message)
    {
        var baseUrl = configuration.GetSection("apibaseurl").Value;
        var url = $"{baseUrl}Email";
        
        try {
            // Create JSON payload
            var payload = new {
                email = emailAddress,
                requestCode = code,
                approvalStatus = message
            };
            
            // Send POST request with JSON content
            var response = await http.PostAsJsonAsync(url, payload);
            
            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine($"Email API call failed with status code: {response.StatusCode}");
            }
            return url;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error sending email: {ex.Message}");
        }
        return "url";
    }

}

﻿@inherits LayoutComponentBase
@inject IWebHostEnvironment env
@inject AppDataService service
@inject CorporateService cService
@inject AuthenticationStateProvider AuthenticationStateProvider
<style>
    .mmii {
    }

    .mmii button {
        margin: 0;
        padding: 0;
    }
</style>
<MudThemeProvider/>
<MudDialogProvider/>
<MudSnackbarProvider/>
<Syncfusion.Blazor.Popups.SfDialogProvider/>
<MudPopoverProvider />

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start"
                       OnClick="@(e => DrawerToggle())"></MudIconButton>
        <MudSpacer/>
        <MudText Typo="Typo.h5" Style="font-weight:600">PS SMART MEMO</MudText>
        <MudSpacer/>
        @*<MudIconButton Icon="@Icons.Material.Filled.Notifications" Color="Color.Inherit" Edge="Edge.End" />*@
        <AuthorizeView>
            <Authorized>



                <p style="margin-left: 20px;margin-right:10px;">
                    Welcome: @GetUserFullName(context.User.Identity.Name)</p>
                @{
                    var url = "/uploadUserPics/missing.jpg";
                    var userId = context.User.Identity.Name.Replace("\\", "-");
                    var path = $"{env.WebRootPath}\\uploadUserPics\\" + userId + ".png";
                    if (File.Exists(path))
                    {
                        url = "/uploadUserPics/" + userId + ".png";
                    }

                    <a href="/myprofile">
                        <img src="@url" alt="Alternate Text"
                             style="width:40px;height:40px;object-fit:cover;border-radius: 50%"/>

                    </a>
                }


            </Authorized>
            <NotAuthorized>
                <p>You are not authorized!</p>
            </NotAuthorized>
        </AuthorizeView>
        <div style="width:10px;"></div>


    </MudAppBar>
    <MudDrawer @bind-Open="_drawerOpen" Elevation="2">
        <MudDrawerHeader>
            <a href="/">
                <div style="display: flex; align-items: center; justify-content:center">
                    <img src="/images/logo.png" alt="Pak Suzuki" width="80%"/>
                </div>
            </a>
        </MudDrawerHeader>
        <NavMenu/>
    </MudDrawer>
    <MudMainContent>
        <div class="c-container">

            @Body
        </div>
    </MudMainContent>
</MudLayout>

@code {
    bool _drawerOpen = true;

    void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    private List<PendingApprovalDto> pendingApprovals = new();

    //private List<PendingApprovalDto> groupList = new List<PendingApprovalDto>();
    private string GetUserFullName(string userId)
    {
        var fullName = service.GetUserFullName(userId).Result;
        return fullName;
    }

    private List<PendingApprovalDto> approvalsList = new();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        var userId = "";
        if (user.Identity is { Name: not null })
        {
            userId = user.Identity.Name;
        }

        approvalsList = await cService.GetEmpPendingApprovals(userId);
        approvalsList = approvalsList.Where(c => c.Total > 0).ToList();
        // generate group list based on project name
        //groupList = approvalsList.Where(x=>x.Direct>0 && !string.IsNullOrEmpty(x.Link)).GroupBy(x => x.ProjectCode).Select(x => new PendingApprovalDto
        //{
        //    ProjectCode = x.Key,
        //    ProjectName = string.IsNullOrEmpty( x.FirstOrDefault().ProjectName) ? x.Key : x.FirstOrDefault().ProjectName,
        //    Total = x.Sum(j=>j.Total),
        //    Link = x.FirstOrDefault().Link
        //}).ToList();
    }

}

﻿@inherits LayoutComponentBase
@inject IWebHostEnvironment env
@inject AppDataService service
@inject CorporateService cService
@inject AuthenticationStateProvider AuthenticationStateProvider
<style>
    .mmii {
    }

    .mmii button {
        margin: 0;
        padding: 0;
    }

    .menu-project-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        color: white;
    }

    .menu-project-icon.memo {
        background-color: #2b88d8;
    }

    .menu-project-icon.ringi {
        background-color: #c43e1c;
    }

    .menu-project-icon.trs {
        background-color: #107c41;
    }
</style>
<MudThemeProvider/>
<MudDialogProvider/>
<MudSnackbarProvider/>
<style>
</style>

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start"
                       OnClick="@(e => DrawerToggle())"/>
        <MudSpacer/>
        <MudText Typo="Typo.h5" Style="font-weight:600">PS SMART MEMO - Employee Portal</MudText>
        <MudSpacer/>
        @*<MudIconButton Icon="@Icons.Material.Filled.Notifications" Color="Color.Inherit" Edge="Edge.End" />*@

        <AuthorizeView>
            <Authorized>
                <MudIconButton Icon="@Icons.Material.Filled.Dashboard"
                             Color="Color.Inherit"
                             Edge="Edge.End"
                             Href="/project-dashboard"
                             Title="Project Dashboard" />

                <MudBadge Content="@approvalsList.Sum(x => x.Total)" Color="Color.Error" Overlap="true">
                    <MudMenu Label="Open Menu" Icon="@Icons.Material.Filled.Notifications" Color="Color.Inherit"
                             Class="mmii" AnchorOrigin="Origin.BottomCenter">
                        @foreach (var item in approvalsList)
                        {
                            <MudMenuItem>
                                <a href="@GetProjectLink(item.ProjectCode)" style="display: flex; align-items: center; gap: 8px; text-decoration: none;">
                                    <div class="menu-project-icon @GetProjectIconClass(item.ProjectCode)">
                                        @if (item.ProjectCode == "MMS")
                                        {
                                            <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Email" />
                                        }
                                        else if (item.ProjectCode == "RMS")
                                        {
                                            <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Description" />
                                        }
                                        else if (item.ProjectCode == "TRS")
                                        {
                                            <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Assignment" />
                                        }
                                        else
                                        {
                                            <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Folder" />
                                        }
                                    </div>
                                    <span>@item.ProjectName (@item.Total)</span>
                                </a>
                            </MudMenuItem>
                        }
                        <MudDivider />
                        <MudMenuItem Href="/project-dashboard">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <MudIcon Icon="@Icons.Material.Filled.Dashboard" Size="Size.Small" />
                                <span>Project Dashboard</span>
                            </div>
                        </MudMenuItem>
                    </MudMenu>
                </MudBadge>

                <p style="margin-left: 20px;margin-right:10px;">
                    Welcome: @GetUserFullName(context.User.Identity.Name)</p>
                @{
                    var url = "/uploadUserPics/missing.jpg";
                    var userId = context.User.Identity.Name.Replace("\\", "-");
                    var path = $"{env.WebRootPath}\\uploadUserPics\\" + userId + ".png";
                    if (File.Exists(path))
                    {
                        url = "/uploadUserPics/" + userId + ".png";
                    }

                    <a href="/myprofile">
                        <img src="@url" alt="Alternate Text"
                             style="width:40px;height:40px;object-fit:cover;border-radius: 50%"/>

                    </a>
                }


            </Authorized>
            <NotAuthorized>
                <p>You are not authorized!</p>
            </NotAuthorized>
        </AuthorizeView>


    </MudAppBar>
    <MudDrawer @bind-Open="_drawerOpen" Elevation="2">
        <MudDrawerHeader>
            <a href="/">
                <div style="display: flex; align-items: center; justify-content:center">
                    <img src="/images/logo.png" alt="Pak Suzuki" width="80%"/>

                </div>
            </a>
        </MudDrawerHeader>
        <EmpNavMenu/>
    </MudDrawer>
    <MudMainContent>
        <div class="c-container">

            @Body
        </div>
    </MudMainContent>
</MudLayout>

@code {
    bool _drawerOpen = true;

    void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    private List<PendingApprovalDto> pendingApprovals = new();

    //private List<PendingApprovalDto> groupList = new List<PendingApprovalDto>();
    private List<PendingApprovalDto> approvalsList = new();

    private string GetUserFullName(string userId)
    {
        var fullName = service.GetUserFullName(userId).Result;
        return fullName;
    }

    private string GetProjectIconClass(string projectCode)
    {
        return projectCode switch
        {
            "MMS" => "memo",
            "RMS" => "ringi",
            "TRS" => "trs",
            _ => ""
        };
    }

    private string GetProjectLink(string projectCode)
    {
        return projectCode switch
        {
            "MMS" => "/memo-approvals",
            "RMS" => "/ringi-approvals",
            "TRS" => "/trs-approvals",
            _ => "/project-dashboard"
        };
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        var userId = "";
        if (user.Identity is { Name: not null })
        {
            userId = user.Identity.Name;
        }

        approvalsList = await cService.GetEmpPendingApprovals(userId);
        approvalsList = approvalsList.Where(c => c.Total > 0).ToList();
        // generate group list based on project name
        //groupList = approvalsList.Where(x => x.Direct > 0 && !string.IsNullOrEmpty(x.Link)).GroupBy(x => x.ProjectCode).Select(x => new PendingApprovalDto
        //{
        //    ProjectCode = x.Key,
        //    ProjectName = string.IsNullOrEmpty(x.FirstOrDefault().ProjectName) ? x.Key : x.FirstOrDefault().ProjectName,
        //    Total = x.Sum(j => j.Total),
        //    Link = x.FirstOrDefault().Link
        //}).ToList();
    }

}
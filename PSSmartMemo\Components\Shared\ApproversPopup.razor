@using PSSmartMemo.DTO
@inject WorklistDataService WorklistService
@inject WatchListDataService WatchListService

<div class="approvers-container">
    @if (approvers.Any())
    {
        <div class="approval-section">
            <div class="approval-grid">
                @foreach (var approver in approvers.OrderBy(a => a.sortOrder))
                {
                    <div class="approval-box">
                        <div class="approver-name">@approver.User</div>
                        <div class="approver-role">@approver.Title</div>
                        <div class="approval-line"></div>
                        <div class="approval-status">
                            @if (!string.IsNullOrEmpty(approver.LastAction))
                            {
                                <span class="action-@(approver.LastAction.ToLower().Replace(" ", "-"))">@approver.LastAction</span>
                                @if (approver.LastActionDate != null && approver.LastAction.ToLower()!="pending")
                                {
                                    <span class="approval-date">@approver.LastActionDate?.ToString("d-MMM-yyyy HH:mm")</span>
                                    @if(!string.IsNullOrEmpty( approver.DelegatedUser))
                                    {
                                        <span class="approval-date">Delegated to: @approver.DelegatedUser</span>
                                    }
                                }
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
    }
    else
    {
        <MudText Typo="Typo.body1">No approvers found for this memo.</MudText>
    }
</div>

<style>
    .approvers-container {
        padding: 2px;
    }

    .approval-section {
        
        
        
    }

    .approval-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }

    .approval-box {
        
        padding: 15px;
        text-align: center;
        min-width: 200px;
    }

    .approver-name {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .approver-role {
        color: #666;
        margin-bottom: 15px;
    }

    .approval-line {
        border-top: 1px solid #000;
        margin: 10px 0;
    }

    .approval-status {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .approval-status span {
        font-weight: 500;
    }

    

    /* Action-specific colors */
    .action-approved {
        color: #28a745;  /* Green for approved */
    }

    .action-rejected {
        color: #dc3545;  /* Red for rejected */
    }

    .action-query {
        color: #17a2b8;  /* Blue for query */
    }

    .action-object {
        color: #fd7e14;  /* Orange for object */
    }

    .action-reply {
        color: #6f42c1;  /* Purple for reply */
    }
    .action-pending {
        color: #1e90ff;
    }
    .approval-date {
        font-size: 0.7em;
        color: #666;
    }

    @@media (max-width: 768px) {
        .approval-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @@media (max-width: 480px) {
        .approval-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

@code {
    [Parameter] public int MemoId { get; set; }
    private List<MemoApproverDto> approvers = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadApprovers();
    }

    private async Task LoadApprovers()
    {
        try
        {
            approvers = await WatchListService.GetMemoApprovers(MemoId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading approvers: {ex.Message}");
        }
    }
}

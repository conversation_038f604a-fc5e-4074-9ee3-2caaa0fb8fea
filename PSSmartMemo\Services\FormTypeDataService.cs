﻿namespace PSSmartMemo.Services;

public class FormTypeDataService(IDbContextFactory<ApplicationDbContext> contextFactory) : IDataService<FormTypeDto>
{
    public Task<string> DeleteById(int id, string user)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.FormTypes
            where a.FormTypeId == id
            select a).FirstOrDefault();
        if (q == null)
            return Task.FromResult("Record not found");
        try
        {
            dc.FormTypes.Remove(q);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<List<FormTypeDto>> GetAll()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.FormTypes
            orderby a.FormTypeTitle
            select new FormTypeDto
            {
                Id = a.FormTypeId,
                Title = a.FormTypeTitle,
                Code = a.FormTypeCode
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<FormTypeDto?> GetById(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.FormTypes
            where a.FormTypeId == id
            select new FormTypeDto
            {
                Id = a.FormTypeId,
                Title = a.FormTypeTitle,
                Code = a.FormTypeCode
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<(FormTypeDto, string)> Save(FormTypeDto entity, string user)
    {
        var dc = contextFactory.CreateDbContext();
        // chec form type already exist or not
        var q =
            (from a in dc.FormTypes where a.FormTypeId != entity.Id && a.FormTypeTitle == entity.Title select a).Any();
        if (q)
            return Task.FromResult((entity, "Form Type Already exists"));
        if (entity.Id == 0)
        {
            var ft = new FormType
            {
                FormTypeTitle = entity.Title,
                FormTypeCode = entity.Code,
                FormTypeCreatedBy = user,
                FormTypeCreatedDate = DateTime.Now
            };
            dc.FormTypes.Add(ft);
            dc.SaveChanges();
            entity.Id = ft.FormTypeId;
            return Task.FromResult((entity, "OK"));
        }
        else
        {
            var ft = dc.FormTypes.FirstOrDefault(c => c.FormTypeId == entity.Id);
            if (ft == null)
                return Task.FromResult((entity, "Record not found"));
            ft.FormTypeTitle = entity.Title;
            ft.FormTypeCode = entity.Code;
            ft.FormTypeModifiedBy = user;
            ft.FormTypeModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult((entity, "OK"));
        }
    }
}
@page "/setup/approver-roles"
@inject ApprovalRoleDataService Service
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Syncfusion.Blazor.Buttons

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Approval Roles" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Approval Roles</MudText>
    <MudButton Size="Size.Small" Variant="MudBlazor.Variant.Filled" Color="Color.Primary"
               OnClick="OpenCreateForm"
               StartIcon="@MudBlazor.Icons.Material.Filled.Add">Create</MudButton>
</div>

<SfGrid DataSource="@approvalRoles" AllowSorting="true" AllowFiltering="true" @ref="grid" Height="calc(100vh - 190px)">
    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field="Title" HeaderText="Title" Width="150"></GridColumn>
        <GridColumn Field="Type" HeaderText="Type" Width="120"></GridColumn>
        <GridColumn Field="UserName" HeaderText="User Name" Width="150"></GridColumn>
        <GridColumn Field="Department" HeaderText="Department" Width="150"></GridColumn>
        <GridColumn Field="Designation" HeaderText="Designation" Width="150"></GridColumn>
        <GridColumn Field="IsActive" HeaderText="Active" Width="100" TextAlign="Syncfusion.Blazor.Grids.TextAlign.Center">
            <Template>
                @{
                    var role = (context as ApprovalRoleDto);
                    @if (role?.IsActive == true)
                    {
                        <span class="badge bg-success">Active</span>
                    }
                    else
                    {
                        <span class="badge bg-danger">Inactive</span>
                    }
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Actions" Width="120" TextAlign="Syncfusion.Blazor.Grids.TextAlign.Center">
            <Template Context="cc">
                @{
                    var obj = cc as ApprovalRoleDto;
                    if (obj != null)
                    {
                        if (obj.Title != "Initiator" && obj.Title!="Generic")
                        {
                            <SfButton CssClass="e-flat" IconCss="e-icons e-edit" OnClick="@(() => EditRole((obj)))"></SfButton>
                            <SfButton CssClass="e-flat" IconCss="e-icons e-delete" OnClick="@(() => DeleteRole((obj)))"></SfButton>
                        }
                    }
                }
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog @ref="dialog" Width="700px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>@dialogTitle</Header>
        <Content>
            <EditForm Model="@role" OnValidSubmit="SaveRole">
                <DataAnnotationsValidator />
                <ValidationSummary />
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Title" @bind-Value="role.Title" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => role.Title)" />
                    </div>

                    <div class="col-md">
                        <SfDropDownList DataSource="typeList" @bind-Value="role.Type" Placeholder="Type" FloatLabelType="FloatLabelType.Always">

                        </SfDropDownList>

                        <ValidationMessage For="@(() => role.Type)" />
                    </div>
                </div>
                @if (role.Type == "Fixed")
                {
                    <div class="row mb-2">
                        <div class="col-md">
                            <SfTextBox Placeholder="Domain ID" @bind-Value="role.UserId" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => role.UserId)" />
                        </div>
                        <div class="col-md">
                            <SfTextBox Placeholder="User Name" @bind-Value="role.UserName" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => role.UserName)" />
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-md">
                            <SfTextBox Placeholder="Email" @bind-Value="role.UserEmail" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => role.UserEmail)" />
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md">
                            <SfTextBox Placeholder="Department" @bind-Value="role.Department" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => role.Department)" />
                        </div>
                        <div class="col-md">
                            <SfTextBox Placeholder="Designation" @bind-Value="role.Designation" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => role.Designation)" />
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-md-6">
                            <SfTextBox Placeholder="HCM Code" @bind-Value="role.HCMCode" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                            <ValidationMessage For="@(() => role.HCMCode)" />
                        </div>
                    </div>
                }
                <div class="row mb-3">
                    <div class="col-md" style="display:flex; gap:10px;flex-direction:column;justify-content:space-between; gap:5px;">
                        <label>Is Active</label>
                        <SfSwitch OnLabel="Yes" OffLabel="No" @bind-Checked="role.IsActive" Label="Is Active"></SfSwitch>
                    </div>

                </div>

                <SfButton CssClass="e-primary" Type="Submit">Save</SfButton>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private List<ApprovalRoleDto> approvalRoles = new();
    private ApprovalRoleDto role = new();
    private string dialogTitle = "Add Approval Role";
    private SfGrid<ApprovalRoleDto> grid;
    private SfDialog dialog;

    protected override async Task OnInitializedAsync()
    {
        approvalRoles = await Service.GetTemplateRolesAsync();
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
        }
    }

    private async Task OpenCreateForm()
    {
        role = new ApprovalRoleDto();
        dialogTitle = "Add Approval Role";
        await dialog.ShowAsync();
    }

    private async Task EditRole(ApprovalRoleDto roleDto)
    {
        role = roleDto;
        dialogTitle = "Edit Approval Role";
        await dialog.ShowAsync();
    }

    private async Task SaveRole()
    {
        var result = await Service.Save(role, userId);
        if (result.Item2 == "OK")
        {
            approvalRoles = await Service.GetTemplateRolesAsync();
            await dialog.HideAsync();
        }
        else
        {
            // Handle error
            // You might want to show an error message using a toast or alert
        }
    }

    private async Task DeleteRole(ApprovalRoleDto roleDto)
    {
        var result = await Service.Delete(roleDto.Id, userId);
        if (result == "OK")
        {
            approvalRoles = await Service.GetTemplateRolesAsync();
        }
        else
        {
            // Handle error
            // You might want to show an error message using a toast or alert
        }
    }

    private string[] typeList = new string[] { "Dynamic", "Fixed", "Generic" };
}

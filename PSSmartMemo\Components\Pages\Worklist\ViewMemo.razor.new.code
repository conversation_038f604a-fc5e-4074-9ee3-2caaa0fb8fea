@code {
    [Parameter] public string? Id { get; set; }
    private int _logId;
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private string password = "";
    private MemoDto memoObj = new();
    private List<MemoApprovalLogDto> approvalLogs = new();
    private MemoApprovalLog currentState = new MemoApprovalLog();
    private List<MemoAttachmentDto> attachments = new();
    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
            }
        }

        if (string.IsNullOrEmpty(userId))
        {
            NavMgr.NavigateTo("/");
        }

        try
        {
            if (Id != null)
            {
                _logId = Convert.ToInt32(Id);
            }
        }
        catch
        {
            _logId = 0;
        }

        if (_logId == 0)
        {
            NavMgr.NavigateTo("/worklist");
        }

        // is this item blong to user
        // if not then(direct to worklist
        var isMyMemoItem = await Service.IsMyMemoItem(_logId, userId);
        if (!isMyMemoItem)
        {
            NavMgr.NavigateTo("/worklist");
        }

        memoObj = await Service.GetMemoByLogId(_logId);
        approvalLogs = await Service.GetMemoApprovalLogs(memoObj.MemoId);
        currentState = await Service.GetCurrentMemoState(_logId);
        canForward = await Service.CanForward(_logId);
        canClose = await Service.CanClose(_logId);

        IsQueryMemo = await Service.IsQueryMemo(_logId);
        IsObjectMemo = await Service.IsObjectMemo(_logId);

        IsObjectFormOpen = false;
        IsQueryFormOpen = false;
        canForward = false;
        canClose = false;

        attachments = await Service.GetMemoAttachments(_logId);
    }

    private bool canForward = false;
    public bool canClose = false;
    private SfToast? toastObj;
    private int? toApproverId = -1;
    private bool IsQueryMemo = false;
    private bool IsObjectMemo = false;
    private bool IsObjectFormOpen = false;
    private bool IsQueryFormOpen = false;
    private List<MemoApproverDto> toApproverList = new();
    private string _userAction = "";

    private async Task PerformAction3(string action)
    {
        canForward = false;
        canClose = false;

        if (_userAction == "approved")
        {
            bool nextApproverExist = await Service.NextApproverExists(_logId);
            if (nextApproverExist)
            {
                canForward = true;
                canClose = true;
            }
            else
            {
                canForward = false;
                canClose = true;
            }
        }
        else if (_userAction == "rejected")
        {

        }
    }

    private async Task PerformForward()
    {
        string msg = await Service.PerformForward(_logId, currentState);
        if (msg == "OK")
        {
            NavMgr.NavigateTo("/worklist");
        }
    }
    
    private async Task PerformAction(string action)
    {
        if (string.IsNullOrEmpty(password))
        {
            await ShowToast("Error", "PIN is required");
            return;
        }

        bool isValidPassword = await adService.ValidatePinAsync(userId, password);
        if (!isValidPassword)
        {
            await ShowToast("Error", "Invalid User Password");
            return;
        }

        if (action == "skip")
        {
            string msg = await Service.PerformSkip(_logId, currentState.DraftComments);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                await ShowToast("Error", msg);
            }
            return;
        }

        IsObjectFormOpen = false;
        IsQueryFormOpen = false;
        _userAction = "";
        if (string.IsNullOrEmpty(currentState.DraftComments))
        {
            await ShowToast("Error", "Please enter comments");
            return;
        }
        if (action == "open-object")
        {
            isValidPassword = await adService.ValidatePinAsync(userId, password);
            if (isValidPassword)
            {
                toApproverList = await Service.GetUpperApprovers(_logId);
                IsObjectFormOpen = true;
                IsQueryFormOpen = false;
                canForward = false;
                canClose = false;
                approvalAction = " (Object)";
            }
            else
            {
                await ShowToast("Error", "Invalid User Password");
            }
        }
        else if (action == "open-query")
        {
            isValidPassword = await adService.ValidatePinAsync(userId, password);
            if (isValidPassword)
            {
                toApproverList = await Service.GetUpperApprovers(_logId);
                IsQueryFormOpen = true;
                IsObjectFormOpen = false;
                canForward = false;
                canClose = false;
                approvalAction = " (Query)";
            }
            else
            {
                await ShowToast("Error", "Invalid User Password");
            }
        }
        else if (action == "approved")
        {
            // if next state available then enable forward button
            isValidPassword = await adService.ValidatePinAsync(userId, password);
            if (isValidPassword)
            {
                var nextApproverExists = await Service.NextApproverExists(_logId);
                canForward = nextApproverExists;
                canClose = !nextApproverExists;
                _userAction = action;
                approvalAction = " (Approve)";
            }
            else
            {
                await ShowToast("Error", "Invalid User Password");
            }
        }
        else if (action == "rejected")
        {
            isValidPassword = await adService.ValidatePinAsync(userId, password);
            if (isValidPassword)
            {
                canForward = false;
                canClose = true;
                _userAction = action;
                approvalAction = " (Reject)";
            }
            else
            {
                await ShowToast("Error", "Invalid User Password");
            }
        }
        else if (action == "object")
        {
            string msg = await Service.PerformObject(_logId, toApproverId, currentState.DraftComments);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                await ShowToast("Error", msg);
            }
        }
        else if (action == "query")
        {
            string msg = await Service.PerformQuery(_logId, toApproverId, currentState.DraftComments);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                await ShowToast("Error", msg);
            }
        }
        else if (action == "reply")
        {
            var msg = await Service.PerformReply(_logId, currentState.DraftComments);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
                await ShowToast("Error", msg);
            }
        }
        else if (action == "close")
        {
            var msg = await Service.PerformClose(_logId, currentState.DraftComments, approvalAction);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                await ShowToast("Error", msg);
            }
        }
        else if (action == "forward")
        {
            var msg = await Service.PerformForward(_logId, currentState);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                await ShowToast("Error", msg);
            }
        }
    }

    private string approvalAction = "";

    private async Task ShowToast(string title, string content, bool isError = true)
    {
        if (toastObj == null) return;
        
        var model = new ToastModel
        {
            Title = title,
            Content = content,
            ShowProgressBar = true,
            Timeout = 5000,
            ShowCloseButton = isError
        };
        
        await toastObj.ShowAsync(model);
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private bool IsInitiator
    {
        get
        {
            var approver = memoObj?.MemoApprovers?.FirstOrDefault(a => a.MemoApproverUserId == userId);
            return approver?.MemoApproverRole?.MemoApproverRoleTitle == "Initiator";
        }
    }
    
    private async Task OpenApproversDialog()
    {
        var parameters = new DialogParameters
        {
            ["MemoId"] = memoObj.MemoId
        };
        
        var options = new MudBlazor.DialogOptions
        {
            CloseOnEscapeKey = true,
            MaxWidth = MaxWidth.Medium,
            FullWidth = true
        };
        
        var dialog = await DialogService.ShowAsync<ApproversPopup>("Memo Approvers", parameters, options);
    }
}

﻿@page "/admin/users"
@using ButtonType = MudBlazor.ButtonType
@using ChangeEventArgs = Microsoft.AspNetCore.Components.ChangeEventArgs
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using InputType = MudBlazor.InputType
@inject AdminDataService service
@inject IWebHostEnvironment env
@inject SfDialogService DialogService


<SfToast @ref="toastObj"></SfToast>
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Users" Url=""></BreadcrumbItem>

    </BreadcrumbItems>
</SfBreadcrumb>
<SfDialog @ref="dlgFormRole" Visible="false" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected User: <b>@obj.Name</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="isChecked" Label="Select All" @onchange="selectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>
            <div class="row">
                <SfGrid @ref="dgMainRole" Height="400px" DataSource="allRole" AllowSelection="true"
                        AllowFiltering="true" AllowSorting="true" Width="500px">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id"
                                    Field="@nameof(RoleDTO.Id)"></GridColumn>
                        <GridColumn Width="350" HeaderText="Roles" Field="@nameof(RoleDTO.Name)"></GridColumn>
                        <GridColumn Field="@nameof(RoleDTO.isSelect)" HeaderTextAlign="TextAlign.Center"
                                    HeaderText="Selected" Width="120" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    var ctx = context as RoleDTO;
                                    <div>
                                        <div style="text-align:center">
                                            <SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox>
                                        </div>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save"
                               Variant="Variant.Filled" OnClick="SaveUserRole">Save
                    </MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel"
                               Variant="Variant.Filled" OnClick="() => dlgFormRole!.HideAsync()">Cancel
                    </MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@*<SfDialog @ref="dlgFormRole" Visible="false" Width="400px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <p>Selected User: @obj.Name</p>
            </div>
            <div class="row">
                <SfListView DataSource="@allRole" Height="300px" ShowCheckBox="true" CheckBoxPosition="Syncfusion.Blazor.Lists.CheckBoxPosition.Right">
                    <ListViewFieldSettings TValue="RoleDTO" Id="Id" Text="Name" IsChecked="isSelect"></ListViewFieldSettings>
                </SfListView>
            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save" Variant="Variant.Filled" OnClick="SaveUserRole">Save</MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel" Variant="Variant.Filled" OnClick="closeRoleDialog">Cancel</MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>*@

<SfDialog @ref="dlgForm" Visible="false" Width="700px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@obj" OnValidSubmit="SaveData">
                <DataAnnotationsValidator/>
                <div class="row mb-2">
                    <div class="col-md-3">
                        <img src="@profilePic" class="rounded-lg" style="width: 150px;height:150px;object-fit: cover;"
                             alt="User Pic"/>
                        <MudFileUpload T="IBrowserFile" Accept=".png, .jpg, .jpeg" FilesChanged="UploadFiles2"
                                       MaximumFileCount="1">
                            <ActivatorContent>
                                <MudButton HtmlTag="label"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           Size="Size.Small"                                           >
                                    Add Picture
                                </MudButton>
                            </ActivatorContent>
                        </MudFileUpload>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="User ID" FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.UserId"
                                   Enabled="@isDisable"/>
                        <ValidationMessage For="@(() => obj.UserId)"/>
                    </div>
                    <div class="col-md">
                        <SfTextBox Placeholder="Employee Code" FloatLabelType="FloatLabelType.Auto"
                                   @bind-Value="obj.EmployeeCode"/>
                        <ValidationMessage For="@(() => obj.EmployeeCode)"/>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col">
                        <SfTextBox Placeholder="Name" FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.Name"/>
                        <ValidationMessage For="@(() => obj.Name)"/>
                    </div>
                    <div class="col">
                        <SfTextBox Placeholder="Email" FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.Email"/>
                        <ValidationMessage For="@(() => obj.Email)"/>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Password" Enabled="@isDisable" FloatLabelType="FloatLabelType.Auto"
                                   @bind-Value="obj.Password" Type="Syncfusion.Blazor.Inputs.InputType.Password"/>
                        <ValidationMessage For="@(() => obj.Password)"/>
                    </div>
                    <div class="col-md">
                        <SfTextBox Placeholder="Confirm Password" Enabled="@isDisable"
                                   FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.ConfirmPassword"
                                   Type="Syncfusion.Blazor.Inputs.InputType.Password"/>
                        <ValidationMessage For="@(() => obj.ConfirmPassword)"/>
                    </div>
                </div>


                <div class="row">
                    <div class="mb-2 col-md">
                        <SfTextBox Placeholder="Note" FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.Note"
                                   Multiline="true"/>
                    </div>
                </div>
                <div style="height:12px;"></div>
                <div class="row mb-2">
                    <div class="col-md">
                        <div class="mb-2">Active</div>
                        <SfSwitch @bind-Checked="@obj.IsActive"></SfSwitch> @obj.Active
                    </div>
                    <div class="col-md">
                        @* <div class="mb-2">Admin</div> *@
                        <SfSwitch @bind-Checked="@obj.IsAdmin" hidden="hidden"></SfSwitch>
                    </div>
                </div>
                <div style="height:12px;"></div>
                <div class="row">
                    <div class="mb-2 col-md">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary"
                                   Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save">
                            Save
                        </MudButton>
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success"
                                   Variant="Variant.Filled" Size="Size.Small"
                                   OnClick="@(async () => { await dlgForm.HideAsync(); })">Cancel
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<div class="row mb-2">
    <div class="col-md">
        <MudText Typo="Typo.h5">Users Account Management</MudText>
    </div>
</div>
<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                   Variant="Variant.Filled" OnClick="OpenCreateForm">Add
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit"
                   Variant="Variant.Filled" OnClick="OpenEditForm">Edit
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete"
                   Variant="Variant.Filled" OnClick="ConfirmDelete">Delete
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Info" StartIcon="@Icons.Material.Filled.Group"
                   Variant="Variant.Filled" OnClick="openAssignRole">Assign Roles
        </MudButton>
    </div>
</div>

<div class="row">
    <div class="mb-2 col-md">
        @if (allUsers.Any())
        {
            <SfGrid @ref="dgMain" Height="calc(100vh -220px)" DataSource="allUsers" AllowSelection="true"
                    AllowFiltering="true" AllowSorting="true" Width="100%">
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn AutoFit="true" Visible="false" HeaderText="ID" Field="@nameof(UserDTO.Id)"></GridColumn>
                    <GridColumn Width="250" HeaderText="Employee Code"
                                Field="@nameof(UserDTO.EmployeeCode)"></GridColumn>
                    <GridColumn Width="250" HeaderText="Name" Field="@nameof(UserDTO.Name)"></GridColumn>
                    @*   <GridColumn Field=@nameof(UserDTO.Name) HeaderText="Name" Width="200" ClipMode="ClipMode.EllipsisWithTooltip">
                <Template>
                @{
                var employee = (context as UserDTO);
                <div>
                <div class="empimg">
                <span class="e-userimg sf-icon-Male"></span>
                </div>
                <span id="Emptext">@employee.Name</span>
                </div>
                }
                </Template>
                </GridColumn> *@
                    <GridColumn Width="250" AutoFit="true" HeaderText="User ID"
                                Field="@nameof(UserDTO.UserId)"></GridColumn>
                    <GridColumn Width="250" HeaderText="Email" Field="@nameof(UserDTO.Email)"></GridColumn>
                    <GridColumn Width="150" HeaderText="Active" Field="@nameof(UserDTO.Active)"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>
<style>
    .e-notecol {
        width: 400px !important;
    }
</style>

@code {

    SfGrid<RoleDTO>? dgMainRole;

    //SHOW PASSWORD WORKING
    bool isShow;
    InputType passwordInput = InputType.Password;
    string passwordInputIcon = Icons.Material.Filled.VisibilityOff;

    void showPwd()
    {
        @if (isShow)
        {
            isShow = false;
            passwordInputIcon = Icons.Material.Filled.VisibilityOff;
            passwordInput = InputType.Password;
        }
        else
        {
            isShow = true;
            passwordInputIcon = Icons.Material.Filled.Visibility;
            passwordInput = InputType.Text;
        }
    }

    bool isShowConfirm;
    InputType PasswordInputConfirm = InputType.Password;
    string PasswordInputIconConfirm = Icons.Material.Filled.VisibilityOff;

    void showPwdConfirm()
    {
        @if (isShowConfirm)
        {
            isShowConfirm = false;
            PasswordInputIconConfirm = Icons.Material.Filled.VisibilityOff;
            PasswordInputConfirm = InputType.Password;
        }
        else
        {
            isShowConfirm = true;
            PasswordInputIconConfirm = Icons.Material.Filled.Visibility;
            PasswordInputConfirm = InputType.Text;
        }
    }
    //SHOW PASSWORD WORKING

    private string searchText = "";
    private List<UserDTO> allUsers = new();
    private UserDTO obj = new();
    private List<RoleDTO> allRole = new();

    private RoleDTO Role = new();

    //[CascadingParameter] public MainLayout Layout { get; set; }
    //[CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }
    bool isDisable = true;
    private string FormTitle = "Add User";

    private string profilePic = "images/profilepic.png";

    SfDialog? dlgForm, dlgFormRole;
    SfGrid<UserDTO>? dgMain;
    private SfToast? toastObj;
    private IBrowserFile? imgfile;

    bool isChecked;
    bool IsFormEditable = true;

    private async void selectAll(ChangeEventArgs args)
    {
        try
        {
            foreach (var item in allRole)
            {
                item.isSelect = isChecked;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            allUsers = await service.GetAllAsync();
            //Layout.PageCaption = "Users";
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task openAssignRole()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var id = dgMain.SelectedRecords[0].Id;
                allRole = await service.GetAllRolesAsyncWithSelectedUser(id);
                obj = await service.GetAsync(id);
                await dlgFormRole!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select user.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task closeRoleDialog()
    {
        await dlgFormRole!.HideAsync();
    }

    private async Task SaveUserRole()
    {
        try
        {
            await service.saveUserRolesInUserScreen(allRole, obj.Id);
            var tm = new ToastModel { Content = "Roles assigned successfully.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
            await closeRoleDialog();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenCreateForm()
    {
        IsFormEditable = true;
        FormTitle = "Add User";
        try
        {
            profilePic = "images/profilepic.png";
            isDisable = true;
            obj = new UserDTO { IsActive = false, IsAdmin = false };
            await dlgForm!.ShowAsync();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    public async Task SaveData()
    {
        // var user = (await authenticationStateTask).User;
        // var op = "";
        // int loginUserId = 0;
        // try
        // {
        //    loginUserId = int.Parse(user.Claims.FirstOrDefault(c => c.Type == "Id").Value);

        // }
        // catch (Exception ex)
        // {
        //    op = ex.Message;
        // }

        // if (op != "")
        // {
        //    var tm = new ToastModel { Content = op, Title = "Error", ShowCloseButton = true , Timeout=0 };
        //    await toastObj.ShowAsync(tm);
        //    return;
        // }
        try
        {
            var vm = await service.IsValid(obj);
            if (vm == "OK")
            {
                var res = await service.SaveAsync(obj, 0);
                await saveImage(obj.UserId.Trim());
                allUsers = await service.GetAllAsync();
                await dlgForm!.HideAsync();
            }
            else
            {
                var tm = new ToastModel { Content = vm, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task saveImage(string userId)
    {
        userId = userId.Replace("\\", "-");
        var dirPath = $"{env.WebRootPath}\\uploadUserPics";
        if (Directory.Exists(dirPath) == false)
        {
            Directory.CreateDirectory(dirPath);
        }

        if (imgfile != null)
        {
            var stream = imgfile.OpenReadStream(1024 * 30 * 1024);
            var path = dirPath + "\\" + userId + ".png";

            var fs = File.Create(path);
            await stream.CopyToAsync(fs);
            stream.Close();
            fs.Close();
            imgfile = null;
        }
    }

    private async Task OpenEditForm()
    {
        FormTitle = "Edit User";
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                isDisable = false;
                var id = dgMain.SelectedRecords[0].Id;
                obj = await service.GetAsync(id);

                var dirPath = $"{env.WebRootPath}\\uploadUserPics";
                if (Directory.Exists(dirPath) == false)
                {
                    Directory.CreateDirectory(dirPath);
                    profilePic = "images/profilepic.png";
                }
                else
                {
                    var path = dirPath + "\\" + obj.UserId.Replace("\\", "-") + ".png";
                    if (File.Exists(path))
                    {
                        profilePic = "uploadUserPics\\" + obj.UserId.Replace("\\", "-") + ".png?id=" + Guid.NewGuid();
                    }
                    else
                    {
                        profilePic = "images/profilepic.png";
                    }
                }

                StateHasChanged();
                await dlgForm!.ShowAsync();
            }
            else
            {
                var tm = new ToastModel { Content = "Please select user.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task ConfirmDelete()
    {
        try
        {
            if (dgMain!.SelectedRecords is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                var id = dgMain.SelectedRecords[0].Id;
                if (conf)
                {
                    var res = await service.DeleteAsync(id);
                    if (res == "OK")
                    {
                        allUsers = await service.GetAllAsync();
                    }
                    else
                    {
                        var mm = new ToastModel { Content = "The entity cannot be deleted because it is being used by other entities.", Title = "Error", ShowCloseButton = true, Timeout = 5000 };
                        await toastObj!.ShowAsync(mm);
                    }
                }
            }
            else
            {
                var tm = new ToastModel { Content = "Please select user.", Title = "Info", ShowCloseButton = true, Timeout = 5000 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 5000 };
            await toastObj!.ShowAsync(tm);
        }
    }

    // IList<IBrowserFile> files = new List<IBrowserFile>();
    private async void UploadFiles2(IBrowserFile file)
    {
        imgfile = file;
        var memoryStream = new MemoryStream();
        await using var fileStream = file.OpenReadStream(long.MaxValue);
        await fileStream.CopyToAsync(memoryStream);
        var bytes = memoryStream.ToArray();
        profilePic = "data:image/png;base64," + Convert.ToBase64String(bytes);
        //fileSize = await uploadObj.BytesToSizeAsync(file.Size);
        Console.WriteLine("test");
        StateHasChanged();
    }

}

<style>
    .e-grid td.e-active {
        background: #faa601 !important;
    }

</style>
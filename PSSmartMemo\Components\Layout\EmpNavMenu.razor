﻿@inject AppDataService service
@inject CorporateService cpService

@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IWebHostEnvironment env


<style>
    .mud-nav-link {
        line-height: 1;
        font-size: 12px;
        display: flex;
        align-items: center;
    }
</style>
<div class="userprofilecard">
    <AuthorizeView>
        <Authorized>
            @{
                var profilePic = "/uploadUserPics/missing.jpg";
                var userId = context.User.Identity.Name.Replace("\\", "-");

                var path = $"{env.WebRootPath}\\uploadUserPics\\" + userId + ".png";

                if (File.Exists(path))
                {
                    profilePic = "/uploadUserPics/" + userId + ".png";
                }

                <img src="@profilePic" alt="Alternate Text"
                     style="width:70px;height:70px;object-fit:cover;border-radius: 50%"/>
                <h3 title="@tooltip" class="mb-0 pb-0">@userName</h3>
                <p class="mt-0 mb-3">@userEmail</p>
                <div style="font-size:10px; width: 95%" class="mb-2">

                    <div class="row">
                        <div class="col-md-4" style="text-align: right">Code</div>
                        <div class="col-md">
                            <b>@userCode</b>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4" style="text-align: right">Designation</div>
                        <div class="col-md">
                            <b>@userDesignation</b>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4" style="text-align: right">Department</div>
                        <div class="col-md" title="@tooltip">
                            <b>@userDepartment</b>
                        </div>
                    </div>
                </div>
                <div class="userlocationcard">
                    <div>@userLocation</div>
                    <div>@userShiftTitle</div>

                </div>
            }
        </Authorized>
    </AuthorizeView>

</div>

<MudNavMenu>

    @if (userMenu.Any())
    {
        var a = (from m in userMenu
            where m.ParentId == 0
            orderby m.SortOrder, m.Name
            select m).ToList();

        foreach (var mi in a)
        {
            var subMenu = (from mm in userMenu
                where mm.ParentId == mi.Id
                select mm).ToList();
            <CompMenuItem menu="@mi" userSubMenu="@subMenu" userMenu="@userMenu"/>
        }
    }


</MudNavMenu>
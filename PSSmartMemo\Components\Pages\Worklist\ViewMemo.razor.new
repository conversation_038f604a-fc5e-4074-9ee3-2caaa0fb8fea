@page "/worklist/viewmemo/{Id}"
@using InputType = Syncfusion.Blazor.Inputs.InputType
@using MudBlazor
@using PSSmartMemo.Components.Shared

@inject WorklistDataService Service
@inject CorporateService adService
@inject IDialogService DialogService
@inject WatchListDataService WatchListService
@attribute [Authorize]
<style>
    .page-block {
        margin-top: 40px;
        padding: 30px;
        padding-left: 30px;
        padding-right: 30px;
        background-color: white;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        border: 1px solid gray
    }
</style>
<SfToast @ref="toastObj"></SfToast>
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Approval list" Url="/worklist"></BreadcrumbItem>
        <BreadcrumbItem Text="@memoObj.MemoTitle" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="page-block">
    <div class="mb-4" style="border-bottom: 1px solid gray;">
        <MudText Typo="Typo.body1" style="color:#aaa">@memoObj.MemoCode</MudText>
        <MudText Typo="Typo.h5">@(memoObj.MemoTitle?.ToUpper() ?? "")</MudText>
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <MudText Typo="Typo.body1" style="color:darkgreen">@memoObj.MemoTypeStr</MudText>
            <MudButton Variant="Variant.Outlined" 
                      Color="Color.Primary" 
                      Size="Size.Small"
                      OnClick="OpenApproversDialog">
                View Approvers
            </MudButton>
        </div>
    </div>
    <div class="tmp-preview-sections">
        @foreach (var sec in memoObj.MemoSections.Where(s => !s.MemoSectionIgnored))
        {
            <div class="tmp-preview-section">
                <div class="tmp-preview-section-title">
                    @sec.MemoSectionTitle
                </div>
                <div class="tmp-preview-section-content" style="overflow-x: auto; max-width: 100%;">
                    @((MarkupString)sec.MemoSectionContentHtml!)
                </div>
            </div>
        }
    </div>
</div>

<div class="page-block">
    <MudText Typo="Typo.h5">Attachments</MudText>
    @if (attachments.Any())
    {
        <table class="approver-table">
            <thead>
                <tr>
                    <th>File Name</th>
                    <th>Type</th>
                    <th>Size</th>
                    <th>Description</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var file in attachments)
                {
                    <tr>
                        <td>@file.Name</td>
                        <td>@file.AttachmentType</td>
                        <td>@FormatFileSize(Convert.ToInt64(file.Size))</td>
                        <td>@file.Description</td>
                        <td>
                            <MudLink Href="@file.Path" Target="_blank">
                                <MudIcon Icon="@Icons.Material.Filled.Download" />
                                Download
                            </MudLink>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    }
    else
    {
        <MudText Typo="Typo.body2" Color="Color.Secondary">No attachments available</MudText>
    }
</div>

<div class="page-block">
    <MudText Typo="Typo.h5">Approval @approvalAction</MudText>
    <div class="row mb-2">
        <div class="col-md">
            <SfTextBox Enabled="!(canClose || canForward || IsObjectFormOpen || IsQueryFormOpen)"
                       Multiline="true" Placeholder="Comments" @bind-Value="currentState.DraftComments" FloatLabelType="FloatLabelType.Always"></SfTextBox>
        </div>
    </div>
    @if (IsQueryFormOpen)
    {
        <div class="row mb-2">
            <div class="col-md">
                <MudText Typo="Typo.h6">Query to User</MudText>
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-md">
                <SfDropDownList DataSource="toApproverList" Placeholder="Approver"
                                @bind-Value="toApproverId"
                                TValue="int?"
                                TItem="MemoApproverDto"
                                FloatLabelType="FloatLabelType.Always">
                    <DropDownListFieldSettings Value="@nameof(MemoApproverDto.MemoApproverId)"
                                               Text="@nameof(MemoApproverDto.Title)"></DropDownListFieldSettings>
                </SfDropDownList>
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-md">
                <MudButton OnClick="@(() => PerformAction("query"))"
                           Color="Color.Primary" Variant="Variant.Filled"
                           Size="Size.Small">Send Query</MudButton>
                <MudButton OnClick="@(() => { IsQueryFormOpen = false;
    IsObjectFormOpen = false;
    canClose = false;
    canForward = false;
    approvalAction = "";
                                    })"
                           Color="Color.Secondary" Variant="Variant.Filled"
                           Size="Size.Small">Previous Screen</MudButton>

            </div>
        </div>
    }
    else if (IsObjectFormOpen)
    {
        <div class="row mb-2">
            <div class="col-md">
                <MudText Typo="Typo.h6">Object to User</MudText>
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-md">
                <SfDropDownList DataSource="toApproverList" Placeholder="Approver"
                                @bind-Value="toApproverId"
                                TValue="int?"
                                TItem="MemoApproverDto"
                                FloatLabelType="FloatLabelType.Always">
                    <DropDownListFieldSettings Value="@nameof(MemoApproverDto.MemoApproverId)"
                                               Text="@nameof(MemoApproverDto.Title)"></DropDownListFieldSettings>
                </SfDropDownList>
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-md">
                <MudButton OnClick="@(() => PerformAction("object"))"
                           Color="Color.Primary" Variant="Variant.Filled"
                           Size="Size.Small">Send Object</MudButton>
                <MudButton OnClick="@(() => { IsObjectFormOpen = false;
    canClose = false;
    canForward = false;
    approvalAction = "";
                                    })"
                           Color="Color.Secondary" Variant="Variant.Filled"
                           Size="Size.Small">Previous Screen</MudButton>


            </div>
        </div>
    }
    else if (IsObjectFormOpen == false && IsQueryFormOpen == false)
    {
        <div class="row mb-2">
            <div class="col-md">
                <div style="display:flex;gap:5px;">
                    <SfTextBox Width="80px" @bind-Value="@password" Type="InputType.Password" FloatLabelType="FloatLabelType.Never" Placeholder="PIN"></SfTextBox>
                    @if (IsQueryMemo || IsObjectMemo)
                    {
                        <MudButton OnClick="@(() => PerformAction("reply"))" Color="Color.Primary" Variant="Variant.Filled" Size="Size.Small">Reply</MudButton>
                    }

                    <MudButton Disabled="@(canForward || canClose || IsQueryMemo || IsObjectMemo)" OnClick="@(() => PerformAction("approved"))" Color="Color.Primary" Variant="Variant.Filled" Size="Size.Small">Approve</MudButton>
                    <MudButton Disabled="@(canForward || canClose || IsQueryMemo)" OnClick="@(() => PerformAction("rejected"))" Color="Color.Warning" Variant="Variant.Filled" Size="Size.Small">Reject</MudButton>
                    <MudButton Disabled="@(canForward || canClose || IsQueryMemo)" OnClick="@(() => PerformAction("open-object"))" Color="Color.Error" Variant="Variant.Filled" Size="Size.Small">Object</MudButton>
                    <MudButton Disabled="@(canForward || canClose || IsObjectMemo || IsQueryMemo)" OnClick="@(() => PerformAction("open-query"))" Color="Color.Info" Variant="Variant.Filled" Size="Size.Small">Query</MudButton>
                    <MudButton Disabled="@(canForward || canClose || IsObjectMemo || IsQueryMemo || IsInitiator)" OnClick="@(() => PerformAction("skip"))" Color="Color.Default" Variant="Variant.Filled" Size="Size.Small">Skip</MudButton>
                    @if (canForward)
                    {
                        <MudButton OnClick="@(() => PerformAction("forward"))" Color="Color.Success" Variant="Variant.Filled" Size="Size.Small">Forward</MudButton>

                    }
                    @if (canClose)
                    {
                        <MudButton OnClick="@(() => PerformAction("close"))" Color="Color.Secondary" Variant="Variant.Filled" Size="Size.Small">Close</MudButton>
                    }
                    @if (canClose || canForward)
                    {
                        <MudButton OnClick="@(() =>
                                     {
                                         canClose = false;
                                         canForward = false;
                                         approvalAction = "";
                                     })" Color="Color.Tertiary" Variant="Variant.Filled" Size="Size.Small">Previous Screen</MudButton>
                    }
                </div>
            </div>
        </div>
    }
</div>

<div class="page-block">
    <MudText Typo="Typo.h5">Approval Log</MudText>
    <table class="approver-table">
        <thead>
            <tr>
                <th>Date and Time</th>
                <th>From User</th>
                <th>Action</th>
                <th>To User</th>
                <th>Comments</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var ii in approvalLogs)
            {
                <tr>
                    <td>@ii.ActionDateTime!.Value.ToString("d MMM, yyyy h:mm")</td>
                    <td>@ii.FromUser</td>
                    <td>@ii.Action</td>
                    <td>@ii.ToUser</td>
                    <td>@ii.Comments</td>
                </tr>
            }
        </tbody>
    </table>
</div>

﻿@page "/memos"
@page "/memos/{StrMemoId}/edit"
@page "/memos/{StrMemoId}/edit/{StrApprovalLogId}"
@inject NavigationManager NavMgr
@inject CorporateService CorpService
@inject MemoDataService Service
@inject WorklistDataService WLService
@inject IJSRuntime js
@inject AdminDataService AdminService
@inject SignalRService SignalRService
@implements IAsyncDisposable
@using ButtonType = MudBlazor.ButtonType
@using FileInfo = System.IO.FileInfo
@rendermode InteractiveServer
@inject IWebHostEnvironment Env
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using Path = Path
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Memos" Url="/my-memo-lister"></BreadcrumbItem>
        <BreadcrumbItem Text="Memos Detail" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<SfDialog @ref="approversDialog" Width="800px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>Memo Approval Logs</Header>
        <Content>
            <div class="approvers-dialog-content">

                <PSSmartMemo.Components.Shared.ApprovalTimeline ApprovalLogs="@approvalLogs"/>
            </div>
        </Content>
        <FooterTemplate>
            <div class="dialog-footer">
                <SfButton CssClass="e-primary" OnClick="@(() => approversDialog.HideAsync())">Close</SfButton>
            </div>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>


<SfDialog @ref="dlgFileUpload" Width="500px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Visible="false">
    <DialogTemplates>
        <Header>Upload Attachment</Header>
        <Content>
            <EditForm Model="@newAttachment" OnValidSubmit="@(async () => await HandleFileUpload())"
                      FormName="AttachmentForm">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="row mb-3">
                    <div class="col-md">
                        <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                                       Accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg"
                                       FilesChanged="FileSelected"
                                       MaximumFileCount="5">
                            <ActivatorContent>
                                <MudButton HtmlTag="label"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.CloudUpload"
                                           for="@context">
                                    Browse Files
                                </MudButton>
                            </ActivatorContent>
                        </MudFileUpload>
                    </div>
                </div>

                @if (selectedFiles != null && selectedFiles.Any())
                {
                    <div class="selected-files mt-2">
                        @foreach (var file in selectedFiles)
                        {
                            <div class="file-item">
                                <i class="material-icons">attach_file</i>
                                <span class="file-name">@file.Name</span>
                                <span class="file-size">@(file.Size / 1024) KB</span>
                            </div>
                        }
                    </div>

                    <style>
                        .selected-files {
                            max-height: 200px;
                            overflow-y: auto;
                            border: 1px solid #e0e0e0;
                            border-radius: 4px;
                            padding: 8px;
                        }

                        .file-item {
                            display: flex;
                            align-items: center;
                            padding: 4px 8px;
                            border-bottom: 1px solid #f0f0f0;
                        }

                        .file-item:last-child {
                            border-bottom: none;
                        }

                        .file-item i {
                            font-size: 18px;
                            color: #666;
                            margin-right: 8px;
                        }

                        .file-name {
                            flex: 1;
                            font-size: 14px;
                            color: #333;
                        }

                        .file-size {
                            font-size: 12px;
                            color: #666;
                            background: #f5f5f5;
                            padding: 2px 6px;
                            border-radius: 3px;
                            margin-left: 8px;
                        }
                    </style>
                }

                <div class="row mb-3">
                    <div class="col-md">
                        <SfDropDownList TValue="int" TItem="AttachmentTypeDto"
                                        @bind-Value="newAttachment.AttachmentTypeId"
                                        DataSource="@attachmentTypes"
                                        Placeholder="Select File Type"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md">
                        <SfTextBox Multiline="true"
                                   Placeholder="Description"
                                   FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="newAttachment.Description">
                        </SfTextBox>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md">
                        <MudButton ButtonType="ButtonType.Submit"
                                   Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   Disabled="@(selectedFiles == null || !selectedFiles.Any())">
                            Upload
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog @ref="dlgSectionPreview" Width="800px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true"
          Visible="false">
    <DialogTemplates>
        <Header>Section Preview - @_sectionTitle</Header>
        <Content>
            @((MarkupString)_sectionText)
        </Content>
    </DialogTemplates>
</SfDialog>

<SfToast @ref="toastObj"></SfToast>


<SfDialog @ref="dlgPreview" Width="800px" Height="90vh" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true"
          Visible="false">
    <DialogTemplates>
        <Header>Template Preview</Header>
        <Content>
            @*             <TemplatePreview TemplateId="templateId"></TemplatePreview> *@
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog @ref="dlgFormApprover" Width="1000px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true"
          Visible="false">
    <DialogTemplates>
        <Header>Search Employee</Header>
        <Content>
            <div class="row">
                <div class="col-md-3 mb-2">
                    <SfDropDownList TValue="string?" TItem="DivisionDto"
                                    DataSource="divisionsList"
                                    Placeholder="Division" FloatLabelType="FloatLabelType.Always"
                                    ShowClearButton="true" AllowFiltering="true" FilterType="FilterType.Contains"
                                    @bind-Value="currentDivision">
                        <DropDownListFieldSettings Value="Code" Text="Name"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="string?" TItem="DivisionDto"
                                            ValueChange="FillDepartments"></DropDownListEvents>

                    </SfDropDownList>
                </div>
                <div class="col-md-3 mb-2">
                    <SfDropDownList FloatLabelType="FloatLabelType.Auto"
                                    AllowFiltering="true" FilterType="FilterType.Contains"
                                    TValue="string?" TItem="DepartmentDto" Placeholder="Department"
                                    DataSource="@departmentsList" @bind-Value="currentDepartment">
                        <DropDownListFieldSettings Text="Name" Value="Code"></DropDownListFieldSettings>
                    </SfDropDownList>

                </div>
                <div class="col-md mb-2">
                    <SfTextBox CssClass="e-outline" Placeholder="Employee Search" @bind-Value="searchText"
                               FloatLabelType="FloatLabelType.Auto"></SfTextBox>
                </div>
                <div class="col-md-1 mb-2" style="display: flex; align-items: flex-end; flex-direction: row-reverse;">
                    <SfButton CssClass="e-small e-primary" IconCss="e-icons e-search" OnClick="SearchEmployee">
                        Search
                    </SfButton>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col-md">
                    <SfGrid DataSource="contacts" AllowFiltering="true" AllowSorting="true"
                            Height="250px;">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn AutoFit="true" HeaderText="Code"
                                        Field="@nameof(EmpContactInfo.EmpNo)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Name"
                                        Field="@nameof(EmpContactInfo.Name)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Designation"
                                        Field="@nameof(EmpContactInfo.Designation)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Department"
                                        Field="@nameof(EmpContactInfo.Department)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Email"
                                        Field="@nameof(EmpContactInfo.Email)"></GridColumn>
                            <GridColumn AutoFit="true" HeaderText="Action">
                                <Template Context="cc">
                                    @{
                                        if (cc is EmpContactInfo obj)
                                        {
                                            <SfButton CssClass="e-small e-success" IconCss="e-icons e-check"
                                                      OnClick="@(() => SelectEmployee(obj))">
                                                Select
                                            </SfButton>
                                        }
                                    }
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfDialog Width="600px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true"
          Visible="false">
    <DialogTemplates>
        <Header>Add/Edit Memo Approver</Header>
        <Content>
            <EditForm Model="SelectedApprover" OnValidSubmit="SaveApprover">
                <div class="row">
                    <div class="col-md mb-2">
                        <DataAnnotationsValidator></DataAnnotationsValidator>
                        <ValidationSummary></ValidationSummary>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="User Id"
                                   FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="SelectedApprover.UserId">
                        </SfTextBox>
                    </div>
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Email" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="SelectedApprover.Email">
                        </SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Name" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="SelectedApprover.User">
                        </SfTextBox>
                    </div>
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Designation" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="SelectedApprover.MemoApproverDesignation">
                        </SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <MudButton Style="margin-right:10px;" Size="Size.Small" Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   ButtonType="ButtonType.Submit"
                                   StartIcon="@Icons.Material.Filled.Add">
                            Save
                        </MudButton>

                        <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Error"
                                   ButtonType="ButtonType.Button"
                                   StartIcon="@Icons.Material.Filled.Close" OnClick="HideApproverDialog">
                            Close
                        </MudButton>
                    </div>
                </div>
            </EditForm>

        </Content>
    </DialogTemplates>
</SfDialog>


<div class="container">
    <div class="row mt-1">
        <div class="col-md" style="text-align: end !important; display: flex; gap: 10px; justify-content: end;">
            @if (isProgress)
            {
                <MudText Typo="Typo.caption" Color="Color.Info">Please Wait...</MudText>
                <MudProgressCircular Size="Size.Small" Color="Color.Info" Indeterminate="true"/>
            }
            else
            {
                var saveText = isLogAtObjectState ? "Update" : "Save";
                try
                {
                    memoId = int.Parse(StrMemoId);
                }
                catch (Exception)
                {
                }

                @if (memoId > 0)
                {
                    <MudLink Href="@($"/memos/{memoDto.MemoId}/preview")"
                             Target="_blank"
                             Style="text-decoration: none;">
                        <MudButton Size="Size.Small"
                                   Variant="Variant.Filled"
                                   Color="Color.Info"
                                   StartIcon="@Icons.Material.Filled.Preview">
                            Preview
                        </MudButton>
                    </MudLink>
                }


                <MudButton Style="margin-right:10px;" Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
                           ButtonType="ButtonType.Button" Disabled="@(memoDto.MemoStatus == "PUBLISHED")"
                           StartIcon="@Icons.Material.Filled.Save" OnClick="@(() => SaveData("DRAFT"))">
                    @saveText
                </MudButton>
                {
                    try
                    {
                        memoId = int.Parse(StrMemoId);
                    }
                    catch (Exception)
                    {
                    }

                    @if (memoId > 0)
                    {
                        if (approvalLogId == 0)
                        {
                            <MudButton Style="margin-right:10px;" Size="Size.Small" Variant="Variant.Filled"
                                       Color="Color.Success"
                                       ButtonType="ButtonType.Button"
                                       Disabled="@(isSaveButtonDisabled || isLogAtObjectState)"
                                       StartIcon="@Icons.Material.Filled.Save" OnClick="@(() => SaveData("PUBLISHED"))">
                                Save & Forward
                            </MudButton>
                        }
                        else
                        {
                            <MudButton Style="margin-right:10px;" Size="Size.Small" Variant="Variant.Filled"
                                       Color="Color.Success"
                                       ButtonType="ButtonType.Button"
                                       StartIcon="@Icons.Material.Filled.Save" OnClick="ForwardRequest">
                                Forward
                            </MudButton>
                        }
                    }
                }


                @if (approvalLogId > 0)
                {
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               Size="Size.Small"
                               OnClick="OpenApproversDialog">
                        View Approval Logs
                    </MudButton>
                }

                @if (memoId > 0 && memoDto.MemoStatus == "PUBLISHED")
                {
                    <MudButton Style="margin-right:10px;"
                               Size="Size.Small"
                               Variant="Variant.Filled"
                               Color="Color.Secondary"
                               ButtonType="ButtonType.Button"
                               StartIcon="@Icons.Material.Filled.ContentCopy"
                               OnClick="@DuplicateMemo">
                        Duplicate Memo
                    </MudButton>
                }

                @*<MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Error"
                       ButtonType="ButtonType.Button"
                       StartIcon="@Icons.Material.Filled.Close">
                Close
            </MudButton>*@
            }
        </div>
    </div>
    <div class="row">
        <div class="col-md">

            <SfDropDownList Placeholder="My Memos"
                            FloatLabelType="FloatLabelType.Always"
                            DataSource="MemoList"
                            Enabled="@(string.IsNullOrEmpty(StrMemoId))"
                            AllowFiltering="true"
                            PopupWidth="400px"
                            FilterType="FilterType.Contains"
                            @bind-Value="memoId">
                <DropDownListTemplates TItem="MemoDto">
                    <ItemTemplate>
                        <div>
                            @{
                                if (context.MemoId == -1)
                                {
                                    <MudText Typo="Typo.body2" Color="Color.Success"> @context.MemoTitle </MudText>
                                }
                                else
                                {
                                    <MudText Typo="Typo.body2">
                                        <b>@context.MemoCode - @(context.MemoTitle ?? "") </b>
                                    </MudText>
                                    <MudText Typo="Typo.caption"
                                             Style="margin-left:13px !important;margin-top: 1px !important">@context.MemoStatus </MudText>
                                }
                            }
                        </div>
                    </ItemTemplate>
                    <ValueTemplate>
                        <div class="dropdown-width" style="width:100%;height:100%;">
                            <div class="name"
                                 style="margin-top: 5px !important;margin-left: 5px !important;"> @context.MemoTitle </div>
                        </div>
                    </ValueTemplate>
                </DropDownListTemplates>
                <DropDownListFieldSettings Value="@nameof(MemoDto.MemoId)"
                                           Text="@nameof(MemoDto.MemoTitle)">
                </DropDownListFieldSettings>
                <DropDownListEvents TValue="int" TItem="MemoDto" ValueChange="changeMemo"></DropDownListEvents>
            </SfDropDownList>
        </div>
        <div class="col-md">
            <SfDropDownList Placeholder="Memo Templates"
                            Enabled="@tmptddlEnaable"
                            FloatLabelType="FloatLabelType.Always"
                            DataSource="templateList"
                            AllowFiltering="true"
                            PopupWidth="400px"
                            FilterType="FilterType.Contains"
                            @bind-Value="memoDto.MemoTemplateId">
                <DropDownListFieldSettings Value="@nameof(TemplateDto.TemplateId)"
                                           Text="@nameof(TemplateDto.TemplateTitle)">
                </DropDownListFieldSettings>
                <DropDownListEvents TValue="int?" TItem="TemplateDto" ValueChange="changeTemplate"></DropDownListEvents>
            </SfDropDownList>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md-9">
            <div class="row">
                <div class="col-md">
                    <SfTextBox Placeholder="Memo Title"
                               FloatLabelType="FloatLabelType.Always"
                               @bind-Value="memoDto.MemoTitle">
                    </SfTextBox>
                </div>
                @* <div class="col-md-4"> *@
                @*     <SfDropDownList Placeholder="Memo Types" *@
                @*                     FloatLabelType="FloatLabelType.Always" *@
                @*                     DataSource="memoTypes" *@
                @*                     AllowFiltering="true" *@
                @*                     PopupWidth="400px" *@
                @*                     FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains" *@
                @*                     @bind-Value="memoDto!.MemoTypeId"> *@
                @*         <DropDownListFieldSettings Value="@nameof(MemoTypeDto.Id)" *@
                @*                                    Text="@nameof(MemoTypeDto.Title)"></DropDownListFieldSettings> *@
                @*     </SfDropDownList> *@
                @* </div> *@
            </div>
            <div class="row">
                <div class="col-md mb-3">
                    @foreach (var sec in memoSectionDTO)
                    {
                        <div class="row mt-4">
                            <div class="col-md">
                                <MudText Style="@($"color:{Colors.Indigo.Default};font-weight:700;")"
                                         Color="Color.Primary" Typo="Typo.subtitle2">@sec.TemplateSectionTitle</MudText>
                            </div>
                            <div class="col-md-2" style="text-align:end !important;">
                                @{
                                    if (sec.IsRequired == false)
                                    {
                                        <MudText Typo="Typo.caption">Ignore Section</MudText>
                                    }
                                }
                            </div>
                            <div class="col-md-1" style="text-align:end !important;">
                                @{
                                    if (sec.IsRequired == false)
                                    {
                                        <SfSwitch OnLabel="Yes" OffLabel="No"
                                                  @bind-Checked="sec.IgnoreSection"></SfSwitch>
                                    }
                                }
                            </div>
                            <div class="col-md-1" style="text-align:end !important;">
                                <MudIconButton Icon="@Icons.Material.Filled.QuestionMark"
                                               OnClick="@(() => OpenPopover(sec))" Variant="Variant.Outlined"
                                               Color="Color.Primary" Size="Size.Small"/>
                                @*<MudPopover Open="@sec.isOpen" Fixed="true" Class="px-4 pt-2" AnchorOrigin="Origin.BottomRight" TransformOrigin="Origin.BottomRight">
                                                    <div style="text-align:center"><MudText Typo="Typo.h5" Color="MudBlazor.Color.Info">Template Section Preview</MudText></div>
                                                    <MudText Style="@($"color:{Colors.Indigo.Default};font-weight:700;margin-bottom:2px;margin-top:5px;")" Color="MudBlazor.Color.Primary" Typo="Typo.subtitle2">@sec.TemplateSectionTitle</MudText>
                                                    <SfRichTextEditor Readonly Placeholder="Template Section Content"
                                                                       Width="100%"
                                                                      @bind-Value="sec.TemplateContentHtml">
                                                        <RichTextEditorToolbarSettings Items="@Tools" Type="ToolbarType.Expand" />
                                                    </SfRichTextEditor>
                                                    <MudButton OnClick="@(() => OpenPopover(sec))" Class="ml-auto mr-n3 mb-1" Color="Color.Error">Close</MudButton>
                                                </MudPopover>*@
                            </div>
                        </div>
                        @if (sec.IgnoreSection == false)
                        {
                            <div class="row mt-1">
                                <div class="col">
                                    <SfRichTextEditor Placeholder="@sec.Placeholder"
                                                      Width="100%"
                                                      @bind-Value="sec.ContentHtml">
                                        <RichTextEditorToolbarSettings Items="@tools" Type="ToolbarType.Expand"/>
                                        <RichTextEditorEvents
                                            BeforeUploadImage="OnBeforeImageBrowse"
                                            BeforePasteCleanup="OnPasteCleanup"></RichTextEditorEvents>
                                        <RichTextEditorPasteCleanupSettings Prompt="false"  />
                                    </SfRichTextEditor>
                                </div>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
        <div class="col-md-3">
            @if (currentTemplate?.MemoTemplateAttachmentAllowed == true)
            {
                <div class="row mt-4">
                    <div class="col-md">
                        <div style="display:flex;gap:5px;justify-content:space-between;align-items:center">
                            <MudText Typo="Typo.subtitle2">Attachments</MudText>
                            <div style="flex:1; height:2px;background-color:gray"></div>
                            <MudFab StartIcon="@Icons.Material.Filled.Add"
                                    Color="Color.Primary"
                                    Disabled="@(memoDto.MemoStatus == "PUBLISHED" ||
                                              memoDto.MemoTemplateId <= 0 ||
                                              memoDto.MemoTemplateId == null ||
                                              MemoAttachments.Count >= currentTemplate.MemoTemplateAttachmentFileCountAllowed)"
                                    OnClick="OpenFileUploadDialog"
                                    Size="Size.Small">
                            </MudFab>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mt-1">
                        <div class="overflow-auto"
                             style="padding-left:10px;padding-top:5px;;height:200px; background-color:white;border-radius:5%">
                            @foreach (var item in MemoAttachments)
                            {
                                <MudStack Spacing="1">
                                    <MudText Typo="Typo.caption" Color="Color.Success">
                                        <MudLink Target="_Blank" Href="@AppDataService.ConvertToUrl(item.Path)"
                                                 Typo="Typo.caption" Color="Color.Success"
                                                 Underline="Underline.None">@item.Name</MudLink>
                                        <SfButton Disabled="@isSaveButtonDisabled" CssClass="e-small e-flat e-danger"
                                                  IconCss="e-icons e-trash"
                                                  OnClick="@(async () => await deleteAttachment(item))"
                                                  PreventDefault="true">
                                        </SfButton>
                                    </MudText>
                                    <MudText Typo="Typo.caption" Style="@($"color:{Colors.Gray.Default};")">
                                        <b>Size:</b> @item.Size bytes, <b>Type:</b> @item.AttachmentType
                                    </MudText>
                                    <MudSpacer/>
                                </MudStack>
                            }
                        </div>
                    </div>
                </div>
            }
            @if (memoDto.MemoTemplateId > 0)
            {
                <div class="row mt-4">
                    <div class="col-md">
                        <div style="display: flex; align-items: center;justify-content: space-between;gap: 5px">
                            <MudText Typo="Typo.subtitle2">Approvers</MudText>
                            <div style="flex:1; height:2px;background-color:gray"></div>

                        </div>
                    </div>
                </div>
                @*<div class="row">
                        <div class="col-md">
                            
                @apiUrl
                        </div>
                    </div>*@
                <div class="row">
                    <div class="col-md mt-1">
                        <div class="overflow-auto"
                             style="padding-left:11px;padding-top:5px;background-color:white;border-radius:5%">
                            <div
                                style="font-size:10px;background-color: #ffd5d5; border: 1px dotted #ff9898;margin-right:7px;padding:4px">
                                <b>Note:</b> Approvers with multiple roles will be automatically excluded. Only the
                                first approver will be retained.
                            </div>
                            @{
                                foreach (var item in MemoApprovers)
                                {
                                    <table class="ck-table-resized mt-2">
                                        <colgroup>
                                            <col style="width:100%;">
                                        </colgroup>
                                        <tbody>
                                        <tr>
                                            <td>
                                                <div>
                                                        <span style="font-size:12px;">
                                                            <strong>@item.User</strong>
                                                        </span>
                                                    <br>
                                                    <span
                                                        style="font-size:12px;">@(item.MemoApproverDesignation ?? item.Title)</span>
                                                </div>
                                                <div class="d-flex gap-2 mt-2">
                                                    @if (!isSaveButtonDisabled)
                                                    {
                                                        @if (item.allowType == "Optional" && approvalLogId == 0)
                                                        {
                                                            <SfButton CssClass="e-small e-danger"
                                                                      IconCss="e-icons e-trash"
                                                                      OnClick="() => ConfirmDelete(item)">

                                                            </SfButton>
                                                        }

                                                        @if (item.Title == "Generic" && approvalLogId == 0)
                                                        {
                                                            <SfButton CssClass="e-small e-success"
                                                                      IconCss="e-icons e-user"
                                                                      OnClick="() => ShowApproverDialog(item)">

                                                            </SfButton>
                                                        }

                                                        @if (item.Title == "Generic" && approvalLogId == 0)
                                                        {
                                                            <SfButton Disabled="@(item.sortOrder <= 1)"
                                                                      CssClass="e-small e-primary"
                                                                      IconCss="e-icons e-arrow-up"
                                                                      OnClick="() => MoveApproverUp(item)">

                                                            </SfButton>
                                                            <SfButton
                                                                Disabled="@(item.sortOrder >= MemoApprovers.Count - 1)"
                                                                CssClass="e-small e-primary"
                                                                IconCss="e-icons e-arrow-down"
                                                                OnClick="() => MoveApproverDown(item)">

                                                            </SfButton>
                                                        }
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                }
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>


</div>

<style>
/* Custom list styles for Syncfusion RichTextEditor to avoid CSS conflicts */
.e-richtexteditor .e-rte-content ul,
.e-richtexteditor .e-rte-content ol {
    margin: 0 0 1em 1.5em !important;
    padding: 0 !important;
    list-style-position: inside !important;
}
.e-richtexteditor .e-rte-content ul {
    list-style-type: disc !important;
}
.e-richtexteditor .e-rte-content ol {
    list-style-type: decimal !important;
}
.e-richtexteditor .e-rte-content ul ul,
.e-richtexteditor .e-rte-content ol ul,
.e-richtexteditor .e-rte-content ul ol,
.e-richtexteditor .e-rte-content ol ol {
    margin-left: 1.5em !important;
}
</style>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    [Parameter] public string? StrMemoId { get; set; }
    [Parameter] public string? StrApprovalLogId { get; set; }
    private string _userId = "";
    private List<MemoDto> MemoList { get; set; } = new();
    private List<TemplateDto> templateList { get; set; } = new();
    private List<MemoSectionDto> memoSectionDTO { get; set; } = new();
    private MemoDto memoDto = new();
    private SfToast? toastObj;
    private List<RoleDTO> allRole = new();
    private MemoDto selectedMemo = new();
    private SfDialog? dlgFormApprover;
    private List<MemoApprovalLogDto> approvalLogs = new();
    private SfDialog? dlgPreview;
    private int memoId = -1;
    bool tmptddlEnaable = true;
    private List<MemoAttachmentDto> MemoAttachments { get; set; } = new();
    private MudFileUpload<IBrowserFile>? fileUpload;
    bool isProgress;
    private List<MemoTypeDto> MemoTypes { get; set; } = new();
    private List<MemoApproverDto> MemoApprovers { get; set; } = new();
    private MemoApproverDto SelectedApprover { get; set; } = new();
    private bool isSaveButtonDisabled;
    private SfDialog? dlgFileUpload;
    private readonly List<IBrowserFile> selectedFiles = new();
    private List<AttachmentTypeDto> attachmentTypes = new();
    private FileUploadModel newAttachment = new();
    private TemplateDto? currentTemplate;
    private SfDialog approversDialog;
    private int approvalLogId = 0;

    private class FileUploadModel
    {
        public int AttachmentTypeId { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    private Task OpenFilePickerAsync()
    {
        return fileUpload?.OpenFilePickerAsync() ?? Task.CompletedTask;
    }

    private List<DivisionDto> divisionsList = new();

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        // Load attachment types
        attachmentTypes = await AdminService.GetAttachmentTypes();

        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
            }
        }

        // Handle edit mode if StrMemoId is provided
        //if (!string.IsNullOrEmpty(StrApprovalLogId) && int.TryParse(StrApprovalLogId, out var parsedApprovalLogId))
        //{
        //    approvalLogId = parsedApprovalLogId;
        //}
        //memoId = 0;
        try
        {
            memoId = int.Parse(StrMemoId ?? "");
        }
        catch (Exception)
        {
            //memoId = 0;
        }

        approvalLogId = 0;
        try
        {
            approvalLogId = int.Parse(StrApprovalLogId ?? "");
        }
        catch (Exception)
        {
            approvalLogId = 0;
        }

        bool isAlreadyForward = await WLService.IsMemoAlreadyForwarded(approvalLogId);
        if (isAlreadyForward)
        {
            NavMgr.NavigateTo("/worklist");
        }

        //if (!string.IsNullOrEmpty(StrMemoId) && int.TryParse(StrMemoId, out var parsedMemoId))
        if (memoId > 0)
        {
            if (approvalLogId == 0)
            {
                // check is this memo is on objection state then redirect to
                var newApprovalLogId = await WLService.GetMemoObjectLogId(memoId);
                if (newApprovalLogId != 0)
                {
                    // @page "/memos/{StrMemoId}/edit/{StrApprovalLogId}"
                    NavMgr.NavigateTo($"/memos/{memoId}/edit/{newApprovalLogId}", true);
                }
            }

            //memoId = parsedMemoId;
            tmptddlEnaable = false;
            memoDto = await Service.GetMemo(memoId);
            approvalLogs = await WLService.GetMemoApprovalLogs(memoId);
            memoSectionDTO = await Service.GetMemoSections(memoId, memoDto);
            MemoAttachments = await Service.GetMemoAttachments(memoId);
            MemoApprovers = await Service.GetMemoApprovers(memoId, memoDto.MemoTemplateId ?? 0, _userId);
            isLogAtObjectState = await Service.IsMemoAtObjectState(memoId);
            currentTemplate = await Service.GetMemoTemplate(memoId); //templateList.FirstOrDefault(t => t.TemplateId == memoDto.MemoTemplateId);
            // Load attachment types based on memo type
            if (memoDto.MemoTypeId.HasValue)
            {
                attachmentTypes = await AdminService.GetAttachmentTypes(memoDto.MemoTemplateId ?? 0);
            }

            if (memoDto.MemoStatusId != 1)
            {
                isSaveButtonDisabled = true;
            }
        }

        MemoList = await Service.GetMyMemos(_userId);
        MemoList = MemoList.OrderByDescending(x => x.MemoCreatedDate).ToList();

        templateList = await Service.GetAssignTemplates(_userId);
        MemoTypes = await Service.GetMemoTypes();
        apiUrl = await Service.GetDynamicUrl(_userId);
        await GetLoadComponents();
        DeptShort = await Service.GetDeptShort(_userId);
        CodeOnly = Service.GetCodeOnly(_userId);
        divisionsList = await CorpService.GetDivisions();
    }

    private bool isLogAtObjectState;

    private async Task changeMemo(ChangeEventArgs<int, MemoDto> args)
    {
        if (args.Value == -1)
        {
            // Clear everything when selecting "New Memo"
            memoId = -1;
            memoDto = new MemoDto();
            memoSectionDTO.Clear();
            MemoAttachments.Clear();
            MemoApprovers.Clear();
            isSaveButtonDisabled = false;
            tmptddlEnaable = true;
            isLogAtObjectState = false;

            // Reset template-related fields
            memoDto.MemoTemplateId = null;
            currentTemplate = null;

            StateHasChanged();
            return;
        }

        if (args.Value > 0)
        {
            await FillMemo(args.Value);
            currentTemplate = templateList.FirstOrDefault(t => t.TemplateId == memoDto.MemoTemplateId);
        }
    }

    private async Task FillMemo(int newMemoId)
    {
        var newApprovalLogId = await WLService.GetMemoObjectLogId(newMemoId);
        if (newApprovalLogId != 0)
        {
            // @page "/memos/{StrMemoId}/edit/{StrApprovalLogId}"
            NavMgr.NavigateTo($"/memos/{newMemoId}/edit/{newApprovalLogId}", true);
        }

        try
        {
            memoId = newMemoId;
            memoDto = new MemoDto();
            memoSectionDTO.Clear();
            MemoAttachments.Clear();
            MemoApprovers.Clear();
            isSaveButtonDisabled = false;
            tmptddlEnaable = true;
            if (memoId != -1)
            {
                tmptddlEnaable = false;
                memoDto = await Service.GetMemo(memoId);
                memoSectionDTO = await Service.GetMemoSections(memoId, memoDto);
                MemoAttachments = await Service.GetMemoAttachments(memoId);
                MemoApprovers = await Service.GetMemoApprovers(memoId, memoDto.MemoTemplateId ?? 0, _userId);

                isLogAtObjectState = await Service.IsMemoAtObjectState(memoId);

                if (memoDto.MemoStatusId != 1)
                {
                    isSaveButtonDisabled = true;
                }
            }
            else
            {
                memoId = newMemoId;
                memoDto = new MemoDto();
                memoSectionDTO.Clear();
                MemoAttachments.Clear();
                MemoApprovers.Clear();
                isSaveButtonDisabled = false;
                isLogAtObjectState = false;
                tmptddlEnaable = true;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            await ShowToast("Error: " + ex.Message, "e-toast-danger");
        }
    }

    private async void changeTemplate(ChangeEventArgs<int?, TemplateDto> args)
    {
        if (args.Value.HasValue)
        {
            memoSectionDTO = await Service.GetMemoSections(memoId, memoDto);
            MemoApprovers = await Service.GetMemoApprovers(memoId, args.Value.Value, _userId);
            currentTemplate = templateList.FirstOrDefault(t => t.TemplateId == args.Value.Value);

            // Get the memo type ID from the selected template
            //var selectedTemplate = templateList.FirstOrDefault(t => t.TemplateId == args.Value.Value);
            //if (selectedTemplate != null)
            //{
            attachmentTypes = await AdminService.GetAttachmentTypes(args.Value ?? 0);
            // }

            // Clear attachments if template doesn't allow them
            if (currentTemplate?.MemoTemplateAttachmentAllowed == false)
            {
                MemoAttachments.Clear();
                PendingAttachments.Clear();
            }
            // If template allows attachments but has a limit
            else if (currentTemplate?.MemoTemplateAttachmentFileCountAllowed != null)
            {
                // Remove excess attachments if any
                while (MemoAttachments.Count > currentTemplate.MemoTemplateAttachmentFileCountAllowed)
                {
                    var lastAttachment = MemoAttachments.Last();
                    deleteAttachment(lastAttachment);
                }
            }
        }
        else
        {
            currentTemplate = null;
            attachmentTypes.Clear();
        }

        StateHasChanged();
    }

    private async Task OpenPopover(MemoSectionDto tSec)
    {
        //tSec.isOpen = !tSec.isOpen;
        //StateHasChanged();
        _sectionText = tSec.Placeholder;
        _sectionTitle = tSec.TemplateSectionTitle;
        await dlgSectionPreview!.ShowAsync();
    }

    private async Task deleteAttachment(MemoAttachmentDto mAttach)
    {
        var result = await js.InvokeAsync<bool>("confirm", "Are you sure you want to remove this attachment?");
        if (!result) return;

        try
        {
            // Check if it's a pending attachment
            var pending = PendingAttachments.FirstOrDefault(p => p.FileName == mAttach.Name);
            if (pending != null)
            {
                if (File.Exists(pending.TempFilePath))
                    File.Delete(pending.TempFilePath);
                PendingAttachments.Remove(pending);
            }

            // Remove from UI list
            MemoAttachments.Remove(mAttach);

            // If it's a saved attachment (has a path), delete the physical file
            if (!string.IsNullOrEmpty(mAttach.Path))
            {
                var filePath = Path.Combine(Env.WebRootPath, mAttach.Path.TrimStart('/'));
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            await ShowToast("Error: " + ex.Message, "e-toast-danger");
        }
    }

    private async void deleteAttachment_previous(MemoAttachmentDto mAttach)
    {
        MemoAttachments.Remove(mAttach);
        StateHasChanged();
    }

    private async void UploadAttachments(IBrowserFile file)
    {
        try
        {
            //string newFileName = $"{Guid.NewGuid().ToString()}_{file.Name}";

            var random = new Random();
            var randomNumber = random.Next(100000, 999999); // Generate a random 6-digit number
            var newFileName = $"{randomNumber}_{file.Name}";
            var dirPath = $"{Env.WebRootPath}\\Memos\\Attachments";
            if (Directory.Exists(dirPath) == false)
            {
                Directory.CreateDirectory(dirPath);
            }

            ///var path = dirPath + "\\" + file.Name;
            var path = Path.Combine(dirPath, newFileName);
            await using (var stream = file.OpenReadStream(30 * 1024 * 1024))
            await using (var fs = File.Create(path))
            {
                await stream.CopyToAsync(fs);
            }
            //stream.Close();
            //fs.Close();

            MemoAttachmentDto attachment = new() { Name = newFileName, Type = file.ContentType, Path = AppDataService.ConvertToUrl(path), Size = file.Size.ToString() };
            MemoAttachments.Add(attachment);

            StateHasChanged();
        }
        catch (Exception ex)
        {
            await ShowToast("Error: " + ex.Message, "e-toast-danger");
        }
    }

    private async void SaveData(string memoStatus)
    {
        int newMemoId;
        try
        {
            if (memoStatus == "PUBLISHED")
            {
                var ma = MemoApprovers.Any(c => c.User == "Generic" || string.IsNullOrEmpty(c.UserId));
                if (ma)
                {
                    var tm = new ToastModel
                    {
                        Title = "Error",
                        Content = "Generic user is not assigned",
                        ShowProgressBar = true,
                        CssClass = "e-toast-danger",
                        ShowCloseButton = true,
                        Timeout = 5000
                    };
                    await toastObj!.ShowAsync(tm);
                    return;
                }
            }

            try
            {
                if (string.IsNullOrEmpty(memoDto.MemoTitle))
                {
                    await ShowToast("Memo title is required.", "e-toast-danger");
                    return;
                }

                if (memoDto.MemoTemplateId == -1 || memoDto.MemoTemplateId == null)
                {
                    await ShowToast("Please select Memo Template.", "e-toast-danger");
                    return;
                }

                var checkRequiredData = await CheckSectionRequiredData();
                if (checkRequiredData == false)
                {
                    await ShowToast("Memo Section content is required.", "e-toast-danger");
                    return;
                }

                if (memoStatus != "DRAFT")
                {
                    if (memoDto.MemoId > 0)
                    {
                        var missingApprover = MemoApprovers.Any(c => string.IsNullOrEmpty(c.UserId));
                        if (missingApprover)
                        {
                            await ShowToast("Not all approvers are defined", "e-toast-danger");
                        }
                    }

                    if (MemoApprovers.Any(x => x.Title != "Initiator") == false)
                    {
                        await ShowToast("Memo Approver is required.", "e-toast-danger");
                        return;
                    }
                }

                if (MemoAttachments.Any() == false)
                {
                    var conf = await DialogService.ConfirmAsync("Are you sure you want to continue the process without File Attachment", "Confirm");
                    if (conf == false)
                        return;
                }

                isProgress = true;
                await Task.Delay(500);

                var updatedMemoId = await Service.SaveMemo(memoId, memoDto, memoSectionDTO, MemoAttachments, MemoApprovers, _userId, memoStatus, isLogAtObjectState);
                memoId = updatedMemoId;
                if (updatedMemoId > 0)
                {
                    foreach (var pending in PendingAttachments)
                    {
                        var targetDir = Path.Combine(Env.WebRootPath, "Memos", "Attachments", memoId.ToString());
                        if (!Directory.Exists(targetDir))
                            Directory.CreateDirectory(targetDir);

                        var targetFileName = $"{pending.TempId}{Path.GetExtension(pending.FileName)}";
                        var targetPath = Path.Combine(targetDir, targetFileName);

                        File.Move(pending.TempFilePath, targetPath);

                        var attachmentDto = new MemoAttachmentDto
                        {
                            MemoId = memoId,
                            Name = pending.FileName,
                            Path = AppDataService.ConvertToUrl(targetPath),
                            Type = pending.ContentType,
                            Size = new FileInfo(targetPath).Length.ToString(),
                            Description = pending.Description,
                            AttachmentTypeId = pending.AttachmentTypeId,
                            AttachmentType = pending.AttachmentType
                        };
                    }

                    PendingAttachments.Clear();
                    MemoAttachments = await Service.GetMemoAttachments(memoId);
                    StateHasChanged();

                    await ShowToast("Memo saved successfully.");

                    // Send email notification if Save & Forward is pressed
                    if (memoStatus == "PUBLISHED")
                    {
                        // Find the next approver (first non-initiator approver)
                        var nextApprover = MemoApprovers.FirstOrDefault(x => x.Title != "Initiator");
                        if (nextApprover != null && !string.IsNullOrEmpty(nextApprover.Email))
                        {
                            UserDTO? userInfo = await Service.GetUserInfo(_userId);
                            var emailMessage = $"A new memo '{memoDto.MemoTitle}' requires your approval.\n\n" +
                                               $"Memo Code: {memoDto.MemoCode}\n" +
                                               (userInfo == null ?
                                                $"From: {_userId}\n" :
                                                    $"From: {userInfo.Name} - ({_userId})\n")
                                                    +
                                               $"Please review you Approval List and take necessary action.";

                            await CorpService.SendEmailAsync(
                                nextApprover.Email,
                                $"Smart Memo - {memoDto.MemoCode ?? ""} - {memoDto.MemoTitle}",
                                emailMessage
                            );
                        }

                        // Only reset state if memo is published
                        memoId = -1;
                        memoDto = new MemoDto { MemoTemplateId = -1 };
                        tmptddlEnaable = true;
                        MemoAttachments.Clear();
                        memoSectionDTO.Clear();
                        MemoApprovers.Clear();
                        MemoList = await Service.GetMyMemos(_userId);
                        MemoList = MemoList.OrderByDescending(x => x.MemoCreatedDate).ToList();
                    }
                    else
                    {
                        // For draft saves, just refresh the current memo data
                        MemoList = await Service.GetMyMemos(_userId);
                        memoId = updatedMemoId;
                        tmptddlEnaable = false;
                        memoDto = await Service.GetMemo(updatedMemoId);
                        memoSectionDTO = await Service.GetMemoSections(updatedMemoId, memoDto);
                        MemoAttachments = await Service.GetMemoAttachments(updatedMemoId);
                        MemoApprovers = await Service.GetMemoApprovers(updatedMemoId, memoDto.MemoTemplateId ?? 0, _userId);

                        if (memoDto.MemoStatusId != 1)
                        {
                            isSaveButtonDisabled = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await ShowToast("Error: " + ex.Message, "e-toast-danger");
            }
            finally
            {
                isProgress = false;
                await Task.Delay(500);
                StateHasChanged();
            }
        }
        catch (Exception e)
        {
            // ignore
        }
    }

    private async Task GetLoadComponents()
    {
        isProgress = true;
        await Task.Delay(1000);
        isProgress = false;
    }

    async Task ShowToast(string msg, string cssClass = "e-toast-success")
    {
        await toastObj!.ShowAsync(new ToastModel
        {
            Content = msg,
            CssClass = cssClass,
            Icon = "e-success toast-icons",
            ShowCloseButton = true,
            ShowProgressBar = true
        });
    }

    private async Task<bool> CheckSectionRequiredData()
    {
        foreach (var item in memoSectionDTO)
        {
            if (item.IsRequired || item.IgnoreSection == false)
            {
                if (string.IsNullOrEmpty(item.ContentHtml))
                {
                    return await Task.FromResult(false);
                }
            }
        }

        return await Task.FromResult(true);
    }

    private async Task ShowApproverDialog(MemoApproverDto memoAppr)
    {
        SelectedApprover = memoAppr;
        await dlgFormApprover!.ShowAsync();
    }

    private async Task HideApproverDialog()
    {
        SelectedApprover = new MemoApproverDto();
        await dlgFormApprover!.HideAsync();
    }

    private async void addApprover()
    {
        try
        {
        }
        catch (Exception ex)
        {
            await ShowToast("Error: " + ex.Message);
        }
    }

    private List<ToolbarItemModel> tools =
    [
        new () { Command = ToolbarCommand.Bold },
        new (){ Command = ToolbarCommand.Italic },
        new (){ Command = ToolbarCommand.Underline },
        new (){ Command = ToolbarCommand.Undo },
        new (){ Command = ToolbarCommand.Redo },
        new (){ Command = ToolbarCommand.Formats },
        new (){ Command = ToolbarCommand.Alignments },
        new (){ Command = ToolbarCommand.FontColor },
        new (){ Command = ToolbarCommand.CreateTable },
        new (){ Command = ToolbarCommand.StrikeThrough },
        new (){ Command = ToolbarCommand.FontSize },
        new (){ Command = ToolbarCommand.LowerCase },
        new (){ Command = ToolbarCommand.UpperCase },
        new (){ Command = ToolbarCommand.SuperScript },
        new (){ Command = ToolbarCommand.SubScript },
        new (){ Command = ToolbarCommand.Separator },
        new (){ Command = ToolbarCommand.OrderedList },
        new (){ Command = ToolbarCommand.UnorderedList },
        new (){ Command = ToolbarCommand.Outdent },
        new (){ Command = ToolbarCommand.Indent },
        new (){ Command = ToolbarCommand.Separator },
        new (){ Command = ToolbarCommand.ClearFormat },
        new (){ Command = ToolbarCommand.NumberFormatList },
        //new (){ Command = ToolbarCommand.SourceCode },




    ];


    private async Task SaveApprover()
    {
        var msg = await Service.SaveMemoApprover(SelectedApprover, _userId);
        if (msg != "OK")
        {
            var tm = new ToastModel
            {
                Title = "error",
                Content = msg,
                ShowProgressBar = true,
                Timeout = 5000
            };
            await toastObj!.ShowAsync(tm);
        }
        else
        {
            MemoApprovers = await Service.GetMemoApprovers(memoId, memoDto.MemoTemplateId ?? 0, _userId);
            await dlgFormApprover!.HideAsync();
        }
    }

    private string apiUrl = "";
    private string? _sectionText = "";
    private string? _sectionTitle = "";
    SfDialog? dlgSectionPreview;

    private void PreviewMemo()
    {
        NavMgr.NavigateTo($"/memos/{memoDto.MemoId}/preview");
        //throw new NotImplementedException();
    }

    private string CodeOnly = "";
    private string DeptShort = "";

    private async Task ConfirmDelete(MemoApproverDto item)
    {
        var res = await js.InvokeAsync<bool>("confirm", "Are you sure want to remove this approver?");
        if (res)
        {
            MemoApprovers = MemoApprovers.Where(c => c.Id != item.Id).ToList();
        }
    }

    private string? currentDivision = " ";
    private string? currentDepartment = " ";
    private List<DepartmentDto> departmentsList = new();
    private string searchText = "";
    private List<EmpContactInfo> contacts = new();


    private async Task FillDepartments(ChangeEventArgs<string?, DivisionDto> obj)
    {
        var code = (obj.ItemData.Code ?? "").Trim();

        //currentDivision = obj.ItemData.Code ?? "";
        departmentsList = string.IsNullOrEmpty(code) ? new List<DepartmentDto>() : await CorpService.GetDepartments(code);
        departmentsList.Insert(0, new DepartmentDto { Code = " ", Name = " - Any Department -" });
        currentDepartment = " ";
    }

    private async Task SearchEmployee()
    {
        contacts = await CorpService.GetEmployeeContacts(searchText, currentDivision, currentDepartment);
    }

    private async Task SelectEmployee(EmpContactInfo empContactInfo)
    {
        SelectedApprover.Email = empContactInfo.Email.ToLower();
        SelectedApprover.UserId = empContactInfo.EmpNo;
        SelectedApprover.User = empContactInfo.Name;
        SelectedApprover.Department = empContactInfo.Department;
        SelectedApprover.MemoApproverDesignation = empContactInfo.Designation;
        await dlgFormApprover!.HideAsync();
    }

    private void FileSelected(IReadOnlyList<IBrowserFile> files)
    {
        selectedFiles.Clear();
        foreach (var file in files)
        {
            selectedFiles.Add(file);
        }

        StateHasChanged();
    }

    private readonly List<PendingAttachment> PendingAttachments = new();

    private async Task HandleFileUpload()
    {
        try
        {
            if (!selectedFiles.Any()) return;

            if (newAttachment.AttachmentTypeId == 0)
            {
                await ShowToast("Please select a file type");
                return;
            }

            // Check if we've reached the maximum number of files
            if (currentTemplate?.MemoTemplateAttachmentFileCountAllowed != null &&
                MemoAttachments.Count + selectedFiles.Count > currentTemplate.MemoTemplateAttachmentFileCountAllowed)
            {
                await ShowToast($"Maximum number of attachments ({currentTemplate.MemoTemplateAttachmentFileCountAllowed}) would be exceeded");
                return;
            }

            // Check total file size (convert bytes to MB)
            var totalSizeInMb = selectedFiles.Sum(f => f.Size) / (1024.0 * 1024.0);
            if (currentTemplate?.MemoTemplateAttachmentPerFileSizeMbAllowed != null &&
                totalSizeInMb > currentTemplate.MemoTemplateAttachmentPerFileSizeMbAllowed)
            {
                await ShowToast($"Total file size exceeds maximum allowed size of {currentTemplate.MemoTemplateAttachmentPerFileSizeMbAllowed}MB");
                return;
            }

            // Create temp directory
            var tempDir = Path.Combine(Env.WebRootPath, "temp");
            if (!Directory.Exists(tempDir))
                Directory.CreateDirectory(tempDir);

            foreach (var file in selectedFiles)
            {
                var fileGuid = Guid.NewGuid();
                var tempFileName = $"{fileGuid}{Path.GetExtension(file.Name)}";
                var tempFilePath = Path.Combine(tempDir, tempFileName);

                await using (var stream = file.OpenReadStream(30 * 1024 * 1024))
                await using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await stream.CopyToAsync(fileStream);
                }

                // Add to pending
                PendingAttachments.Add(new PendingAttachment
                {
                    TempFilePath = tempFilePath,
                    ActualFilePath = tempFilePath,
                    FileName = file.Name,
                    ContentType = file.ContentType,
                    Description = newAttachment.Description,
                    AttachmentTypeId = newAttachment.AttachmentTypeId,
                    AttachmentType = attachmentTypes.FirstOrDefault(a => a.Id == newAttachment.AttachmentTypeId)?.Title ?? "",
                    Size = file.Size,
                    TempId = fileGuid.ToString()
                });

                // Update UI list
                MemoAttachments.Add(new MemoAttachmentDto
                {
                    Name = file.Name,
                    Description = newAttachment.Description,
                    AttachmentType = attachmentTypes.FirstOrDefault(a => a.Id == newAttachment.AttachmentTypeId)?.Title ?? "",
                    Size = file.Size.ToString(),
                    AttachmentTypeId = newAttachment.AttachmentTypeId,
                    Path = tempFilePath,
                    Type = file.ContentType,
                    TempId = fileGuid.ToString()
                });
            }

            await dlgFileUpload!.HideAsync();
            selectedFiles.Clear();
            newAttachment = new FileUploadModel();
            StateHasChanged();

            await ShowToast("Files uploaded (pending save)");
        }
        catch (Exception ex)
        {
            await ShowToast("Error: " + ex.Message);
        }
    }

    private async Task OpenFileUploadDialog()
    {
        selectedFiles.Clear();
        newAttachment = new FileUploadModel();
        await dlgFileUpload!.ShowAsync();
    }

    private void MoveApproverUp(MemoApproverDto approver)
    {
        var index = MemoApprovers.IndexOf(approver);
        if (index > 1) // Prevent moving above initiator (index 0)
        {
            var temp = MemoApprovers[index];
            MemoApprovers[index] = MemoApprovers[index - 1];
            MemoApprovers[index - 1] = temp;

            // Update sort orders
            MemoApprovers[index].sortOrder = index;
            MemoApprovers[index - 1].sortOrder = index - 1;
        }
    }

    private void MoveApproverDown(MemoApproverDto approver)
    {
        var index = MemoApprovers.IndexOf(approver);
        if (index < MemoApprovers.Count - 1)
        {
            var temp = MemoApprovers[index];
            MemoApprovers[index] = MemoApprovers[index + 1];
            MemoApprovers[index + 1] = temp;

            // Update sort orders
            MemoApprovers[index].sortOrder = index;
            MemoApprovers[index + 1].sortOrder = index + 1;
        }
    }

    private async Task DuplicateMemo()
    {
        try
        {
            var result = await js.InvokeAsync<bool>("confirm", "Do you want to duplicate this memo?");
            if (!result) return;

            // Show loading indicator
            // await js.InvokeVoidAsync("showLoading");

            // Duplicate the memo
            var newMemoId = await Service.DuplicateMemo(memoId, _userId);

            // Refresh memo list
            MemoList = await Service.GetMyMemos(_userId);
            MemoList = MemoList.OrderByDescending(x => x.MemoCreatedDate).ToList();

            // Select the new memo for editing
            await FillMemo(newMemoId);

            await ShowToast("Memo duplicated successfully.");
        }
        catch (Exception ex)
        {
            await ShowToast("Error: " + ex.Message);
        }
    }

    private async Task OpenApproversDialog()
    {
        await approversDialog.ShowAsync();
    }

    private async Task ForwardRequest()
    {
        try
        {
            approvalLogId = int.Parse(StrApprovalLogId ?? "");
        }
        catch (Exception)
        {
            // ignored
        }

        var alreadyForwarded = await WLService.IsMemoAlreadyForwarded(approvalLogId);

        if (alreadyForwarded)
            NavMgr.NavigateTo("/worklist", true);
        else
        {
            var res = await js.InvokeAsync<bool>("confirm", "are you sure want to send it for approval?");
            if (res)
            {
                var msg = WLService.PerformReply(approvalLogId, "Updated", _userId);
                NavMgr.NavigateTo("/worklist", true);
            }
        }
    }

    private void OnBeforeImageBrowse(ImageUploadingEventArgs obj)
    {
        obj.Cancel = true;
    }

    private void OnPasteCleanup(PasteCleanupArgs args)
    {
        // Optionally, further clean up the HTML here if needed
        // Example: Remove extra Word-specific tags or attributes missed by built-in cleanup
        if (!string.IsNullOrEmpty(args.Value))
        {
            // Remove Word comments and tags
            args.Value = System.Text.RegularExpressions.Regex.Replace(args.Value, @"<!--\[if.*?endif\]-->", string.Empty, System.Text.RegularExpressions.RegexOptions.Singleline);
            // Remove mso- styles
            args.Value = System.Text.RegularExpressions.Regex.Replace(args.Value, @"mso-[^:]+:[^;']+;?", string.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            // Remove any remaining Word-specific tags
            args.Value = System.Text.RegularExpressions.Regex.Replace(args.Value, @"<(o|w|v):[^>]+>.*?<\/(o|w|v):[^>]+>", string.Empty, System.Text.RegularExpressions.RegexOptions.Singleline);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await SignalRService.InitializeAsync();
        SignalRService.OnMemoUpdate += HandleMemoUpdate;
    }

    private async void HandleMemoUpdate(int updatedMemoId, string action, string fromUser, string toUser)
    {
        // Only refresh if we're not the sender
        if (fromUser != _userId && (action == "PUBLISHED" || action == "UPDATED"))
        {
            await InvokeAsync(async () =>
            {
                MemoList = await Service.GetMyMemos(_userId);
                MemoList = MemoList.OrderByDescending(x => x.MemoCreatedDate).ToList();
                StateHasChanged();
            });
        }
    }

    public async ValueTask DisposeAsync()
    {
        SignalRService.OnMemoUpdate -= HandleMemoUpdate;
        await ValueTask.CompletedTask;
    }
}


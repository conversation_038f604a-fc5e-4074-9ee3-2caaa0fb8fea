@inject CorpApprovalDataService CorpAprService

<SfGrid DataSource="@approvalList" AllowFiltering="true" AllowSorting="true">
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        @foreach (var column in gridColumns)
        { 
            <GridColumn Field="@column.Field" 
                       HeaderText="@column.HeaderText" 
                       Format="@column.Format"
                       Width="@column.Width">
            </GridColumn>
        }
    </GridColumns>
</SfGrid>

@code {
    [Parameter] public string ProjectCode { get; set; } = "";
    [Parameter] public string ApprovalType { get; set; } = "";
    string userId = "";
    private List<dynamic> approvalList = new();
    private List<GridColumnDefinition> gridColumns = new();

    protected override async Task OnParametersSetAsync()
    {
        //await LoadData();
    }

    //public async Task LoadData(string projectCode)
    //{
    //    // Configure columns based on project code
    //    ConfigureColumns();

    //    // Load data based on project code and approval type
    //    switch (ProjectCode)
    //    {
    //        case "RMS":
    //            approvalList = await CorpAprService.GetRINGIListAsync(userId,
    //            ApprovalType == "Direct" ? 1 : 2);
    //            break;
    //        case "OTHER_PROJECT":
    //            // Handle other project types
    //            break;
    //        default:
    //            approvalList = new List<dynamic>();
    //            break;
    //    }
    //}

    private void ConfigureColumns()
    {
        gridColumns.Clear();

        switch (ProjectCode)
        {
            case "RMS":
                gridColumns = new List<GridColumnDefinition>
                {
                    new() { Field = "RingiNo", HeaderText = "RINGI No", Width = "120" },
                    new() { Field = "Title", HeaderText = "Title", Width = "200" },
                    new() { Field = "RequestDate", HeaderText = "Request Date",
                           Format = "dd-MMM-yyyy", Width = "130" },
                    // Add more columns specific to RINGI
                };
                break;
            case "OTHER_PROJECT":
                // Define columns for other project types
                break;
        }
    }

    private class GridColumnDefinition
    {
        public string Field { get; set; } = "";
        public string HeaderText { get; set; } = "";
        public string Format { get; set; } = "";
        public string Width { get; set; } = "";
    }
}
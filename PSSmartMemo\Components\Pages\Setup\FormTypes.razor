﻿@page "/setup/form-types"
@inject FormTypeDataService Service
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Syncfusion.Blazor.Buttons
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Form Types" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>
<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Form Types</MudText>
    
    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
               OnClick="OpenCreateForm"
               StartIcon="@MudBlazor.Icons.Material.Filled.Add">Create</MudButton>
</div>

<SfGrid DataSource="@formTypes"  AllowSorting="true" AllowFiltering="true" @ref="grid" Height="calc(100vh - 190px)">
    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field="Id" HeaderText="ID" Width="100" TextAlign="TextAlign.Center"></GridColumn>
        <GridColumn Field="Title" HeaderText="Title" Width="200"></GridColumn>
        <GridColumn Field="Code" HeaderText="Code" Width="200"></GridColumn>
        <GridColumn HeaderText="Actions" Width="150" TextAlign="TextAlign.Center">
            <Template>
                <SfButton CssClass="e-flat" IconCss="e-icons e-edit" OnClick="@(args => EditFormType((context as FormTypeDto)))"></SfButton>
                <SfButton CssClass="e-flat" IconCss="e-icons e-delete" OnClick="@(args => DeleteFormType((context as FormTypeDto)))"></SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>


<SfDialog @ref="dialog" Width="400px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>@dialogTitle</Header>
        <Content>
            <EditForm Model="@formType" OnValidSubmit="SaveFormType">
                <DataAnnotationsValidator />
                <ValidationSummary />
                <SfTextBox Placeholder="Title" @bind-Value="formType.Title" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                <ValidationMessage For="@(() => formType.Title)" />
                <SfTextBox Placeholder="Code" @bind-Value="formType.Code" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                <ValidationMessage For="@(() => formType.Code)" />
                <SfButton CssClass="e-primary" Type="Submit">Save</SfButton>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private List<FormTypeDto> formTypes = new();
    private FormTypeDto formType = new();
    private string dialogTitle = "Add Form Type";
    private SfGrid<FormTypeDto> grid;
    private SfDialog dialog;

    protected override async Task OnInitializedAsync()
    {
        formTypes = await Service.GetAll();
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
        }
    }

    private async Task OpenCreateForm()
    {
        formType = new FormTypeDto();
        dialogTitle = "Add Form Type";
        await dialog.ShowAsync();
    }

    private async Task EditFormType(FormTypeDto formTypeDto)
    {
        formType = formTypeDto;
        dialogTitle = "Edit Form Type";
        await dialog.ShowAsync();
    }

    private async Task SaveFormType()
    {
        var result = await Service.Save(formType, "user");
        if (result.Item2 == "OK")
        {
            formTypes = await Service.GetAll();
            await dialog.HideAsync();
        }
        else
        {
            // Handle error
        }
    }

    private async Task DeleteFormType(FormTypeDto formTypeDto)
    {
        var result = await Service.DeleteById(formTypeDto.Id, userId);
        if (result == "OK")
        {
            formTypes = await Service.GetAll();
        }
        else
        {
            // Handle error
        }
    }
}
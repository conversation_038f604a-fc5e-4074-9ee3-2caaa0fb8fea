﻿@page "/DemoTest"
@using Syncfusion.Blazor.RichTextEditor 



<div class="control-section">
    <SfRichTextEditor>
        <RichTextEditorPasteCleanupSettings Prompt="true"  />
        <p>The Rich Text Editor component is a WYSIWYG ("what you see is what you get") editor that provides the best user experience to create and update the content. Users can format their content using standard toolbar commands.</p>
        <p><b>Key features:</b></p>
        <ul>
            <li>
                <p>Provides <b>IFRAME</b> and <b>DIV</b> modes</p>
            </li>
            <li>
                <p>Capable of handling markdown editing.</p>
            </li>
            <li>
                <p>Contains a modular library to load the necessary functionality on demand.</p>
            </li>
            <li>
                <p>Provides a fully customizable toolbar.</p>
            </li>
            <li>
                <p>Provides HTML view to edit the source directly for developers.</p>
            </li>
            <li>
                <p>Supports third-party library integration.</p>
            </li>
            <li>
                <p>Allows a preview of modified content before saving it.</p>
            </li>
            <li>
                <p>Handles images, hyperlinks, video, hyperlinks, uploads, etc.</p>
            </li>
            <li>
                <p>Contains undo/redo manager.</p>
            </li>
            <li>
                <p>Creates bulleted and numbered lists.</p>
            </li>
        </ul>
        <RichTextEditorToolbarSettings Items="@Tools" />
    </SfRichTextEditor>
</div>
@code {
    private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
{
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Formats },
        new ToolbarItemModel() { Command = ToolbarCommand.Alignments },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
        new ToolbarItemModel() { Command = ToolbarCommand.Image },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.SourceCode },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo }
    };
}
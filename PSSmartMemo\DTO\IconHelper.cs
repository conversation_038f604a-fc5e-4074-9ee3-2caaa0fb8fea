using System.Reflection;

namespace PSSmartMemo.DTO;

public class IconHelper
{
    public static string? GetIconByName(string name)
    {
        try
        {
            var field = typeof(Icons.Material.Filled).GetField(name,
                BindingFlags.Public |
                BindingFlags.Static |
                BindingFlags.FlattenHierarchy);
            return (string)field.GetValue(null);
        }
        catch (Exception e)
        {
            return Icons.Material.Filled.QuestionMark;
        }
    }
}
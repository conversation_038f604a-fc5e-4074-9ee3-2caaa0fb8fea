﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class Memo
{
    public int MemoId { get; set; }

    public string MemoCode { get; set; }

    public string MemoTitle { get; set; }

    public string MemoStatus { get; set; }

    public DateTime? MemoCreatedDate { get; set; }

    public string MemoCreatedBy { get; set; }

    public DateTime? MemoModifiedDate { get; set; }

    public string MemoModifiedBy { get; set; }

    public bool MemoIsActive { get; set; }

    public bool MemoIsDel { get; set; }

    public int? MemoTemplateId { get; set; }

    public int? MemoTypeId { get; set; }

    public int? FormTypeId { get; set; }

    public int CurrentStep { get; set; }

    public string LastAction { get; set; }

    public string NextActivity { get; set; }

    public byte? MemoStatusId { get; set; }

    public string DepartmentCode { get; set; }

    public string MemoDepartment { get; set; }

    public string MemoDivision { get; set; }

    public virtual FormType FormType { get; set; }

    public virtual ICollection<MemoApprovalLog> MemoApprovalLogs { get; set; } = new List<MemoApprovalLog>();

    public virtual ICollection<MemoApprover> MemoApprovers { get; set; } = new List<MemoApprover>();

    public virtual ICollection<MemoAttachment> MemoAttachments { get; set; } = new List<MemoAttachment>();

    public virtual ICollection<MemoSection> MemoSections { get; set; } = new List<MemoSection>();

    public virtual MemoStatus MemoStatusNavigation { get; set; }

    public virtual MemoTemplate MemoTemplate { get; set; }

    public virtual MemoType MemoType { get; set; }
}
@page "/my-memo-lister"
@using PSSmartMemo.DTO
@using PSSmartMemo.Services
@using Syncfusion.Blazor.Grids
@inject MemoDataService MemoDataService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime js
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Memos" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>


<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
    <h1 style="margin: 0; font-size: 1.5rem; font-weight: 600; color: #2c3e50;">My Memos</h1>
    <a href="/memos" style="
            display: inline-block;
            padding: 0.6rem 1rem;
            background: linear-gradient(90deg, #4f8cff 0%, #1abc9c 100%);
            color: #fff;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            box-shadow: 0 2px 8px rgba(44,62,80,0.08);
            transition: background 0.2s, box-shadow 0.2s;
        "
       onmouseover="this.style.background='linear-gradient(90deg, #1abc9c 0%, #4f8cff 100%)';this.style.boxShadow='0 4px 16px rgba(44,62,80,0.15)';"
       onmouseout="this.style.background='linear-gradient(90deg, #4f8cff 0%, #1abc9c 100%)';this.style.boxShadow='0 2px 8px rgba(44,62,80,0.08)';">
        <span style="vertical-align: middle; margin-right: 0.5rem;">
            <svg width="18" height="18" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="vertical-align: middle;">
                <path d="M12 5v14M5 12h14" />
            </svg>
        </span>
        Create New Memo
    </a>
</div>

@if (isLoading)
{
    <p>Loading memos...</p>
}
else if (memos == null || !memos.Any())
{
    <p>No memos found.</p>
}
else
{
    <SfGrid DataSource="@memos" AllowPaging="true" AllowSorting="true" AllowTextWrap="true" Height="calc(100vh - 270px)">
        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel" />
        <GridColumns>
            <GridColumn Field="MemoId" HeaderText="MemoId" Width="90px" TextAlign="TextAlign.Center" />
            <GridColumn Field="MemoCode" HeaderText="Code" Width="200px" />
            <GridColumn Field="MemoTitle" HeaderText="Title" Width="250px"  />
            <GridColumn Field="MemoTypeStr" HeaderText="Type" Width="180px" />
            <GridColumn Field="MemoCreatedDate" HeaderText="Initiate Date" Format="yyyy-MM-dd" Type="ColumnType.Date" Width="130px" />
            <GridColumn Field="MemoStatus" HeaderText="State" Width="100px" />
            <GridColumn HeaderText="Actions">
                <Template Context="cc">
                    @{
                        var mm = cc as MemoDto;
                        if (mm != null)
                        {
                            var lnk = $"/memos/{mm.MemoId}/edit";
                            var trg = $"/memo-{mm.MemoId}";
                            <div style="display:flex;gap:5px;">
                                <a class="e-control e-btn e-lib e-primary" href="@lnk" target="@trg">Edit</a>

                                @if (mm.CanDelete)
                                {
                                    <SfButton CssClass="e-danger" OnClick="@(()=>ConfirmDeleteMemo(mm.MemoId))">Delete</SfButton>
                                }
                            </div>
                        }
                    }

                </Template>
            </GridColumn>
        </GridColumns>
    </SfGrid>
}

@code {
    private List<MemoDto>? memos = new();

    private bool isLoading = true;
    string loginId = "";

    protected override async Task OnInitializedAsync()
    {
        isLoading = true;
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        loginId = user.Identity?.Name ?? ""; // Replace with your actual login logic if needed
        memos = (await MemoDataService.GetMyMemos(loginId)).Where(m => m.MemoId != -1).ToList();

        isLoading = false;
    }

    public async Task ConfirmDeleteMemo(int memoId)
    {
        var confirm = await js.InvokeAsync<bool>("confirm", "Are you sure want to delete this memo?");
        if (confirm)
        {
            string msg = await MemoDataService.DeleteMemo(memoId, loginId);
            if(msg=="OK")
            {
                memos = (await MemoDataService.GetMyMemos(loginId)).Where(m => m.MemoId != -1).ToList();
            }
        }
    }
}

﻿@page "/admin/menus"
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using SortDirection = Syncfusion.Blazor.Grids.SortDirection
@inject SfDialogService DialogService

@* @rendermode InteractiveServer *@

@inject AdminDataService service


<SfToast @ref="toastObj"></SfToast>
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Menus" Url=""></BreadcrumbItem>

    </BreadcrumbItems>
</SfBreadcrumb>
<SfDialog @ref="dlgForm" Visible="false" Width="700px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true"
          MinHeight="auto">
    <DialogTemplates>
        <Header>@FormTitle</Header>
        <Content>
            <EditForm Model="@obj" OnValidSubmit="SaveData">
                <DataAnnotationsValidator/>
                <ValidationSummary/>
                <div class="row" style="align-items:center;">
                    <div class="col-md-2 mb-2">
                        <SfTextBox Placeholder="Code" FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.Code"/>
                        <ValidationMessage For="@(() => obj.Code)"/>
                    </div>
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Title" FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.Name"/>
                        <ValidationMessage For="@(() => obj.Name)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="URL" FloatLabelType="FloatLabelType.Auto" @bind-Value="obj.Url"/>
                        <ValidationMessage For="@(() => obj.Url)"/>
                    </div>
                    <div class="col-md mb-2">
                        <SfDropDownList FloatLabelType="FloatLabelType.Auto"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains"
                                        TValue="string" TItem="urltargets" Placeholder="Url Target"
                                        DataSource="@utargets" @bind-Value="obj.URLTarget">
                            <DropDownListFieldSettings Text="UrlTarget" Value="ID"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => obj.URLTarget)"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <SfDropDownList FloatLabelType="FloatLabelType.Auto"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains"
                                        TValue="int?" TItem="Modules" Placeholder="Module" DataSource="@modules"
                                        @bind-Value="obj.ModuleId">
                            <DropDownListFieldSettings Text="ModuleTitle" Value="ID"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => obj.ModuleId)"/>
                    </div>
                    <div class="col-md-9 mb-2">
                        <SfDropDownList FloatLabelType="FloatLabelType.Auto"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains"
                                        TValue="int?" TItem="MenuDto" Placeholder="Menu Parent"
                                        DataSource="@allParentMenus" @bind-Value="obj.ParentId">
                            <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                        @*   <ValidationMessage For="@(()=>obj.ParentId)" /> *@
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfNumericTextBox @bind-Value="obj.SortOrder" Placeholder="Sort Order"
                                          FloatLabelType="FloatLabelType.Auto"
                                          ShowClearButton="true"
                                          ShowSpinButton="false"
                                          Decimals="0"
                                          Format="###0"/>
                    </div>

                    <div class="col-md" style="display: flex; gap:20px;align-items:flex-end">

                        <SfDropDownList AllowFiltering="true"
                                        FilterType="FilterType.Contains"
                                        FloatLabelType="FloatLabelType.Always"
                                        Placeholder="Icon" DataSource="@MenuIconsList" @bind-Value="obj.Icon">
                            <DropDownListEvents TValue="string" TItem="string"
                                                ValueChange="OnIconChange"></DropDownListEvents>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => obj.Icon)"/>
                        @if (!string.IsNullOrEmpty(IconUrl))
                        {
                            <MudIcon Class="mb-1" Icon="@IconUrl" Size="Size.Large"></MudIcon>
                        }
                    </div>
                </div>
                <div style="height:12px;"></div>
                <div class="row mb-2">
                    <div class="col-md-2 mb-2">
                        <SfSwitch @bind-Checked="obj.IsActive"></SfSwitch> @obj.Active
                    </div>
                </div>
                <div class="row">
                    <div class="col-md mb-2">
                        <MudButton ButtonType="ButtonType.Submit" Color="Color.Primary"
                                   Variant="Variant.Filled" Size="Size.Small" StartIcon="@Icons.Material.Filled.Save">
                            Save
                        </MudButton>
                        <MudButton ButtonType="ButtonType.Button" Color="Color.Success"
                                   Variant="Variant.Filled" Size="Size.Small"
                                   OnClick="@(async () => { await dlgForm?.HideAsync(); })">
                            Cancel
                        </MudButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<div class="row mb-2">
    <div class="col-md">
        <MudText Typo="Typo.h5">Application Menus Management</MudText>
    </div>
</div>
<div class="row mb-2">
    <div class="col-md">
        <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                   Variant="Variant.Filled" OnClick="OpenCreateForm">Add
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Edit"
                   Variant="Variant.Filled" OnClick="OpenEditForm">Edit
        </MudButton>
        <MudButton Size="Size.Small" Color="Color.Warning" StartIcon="@Icons.Material.Filled.Delete"
                   Variant="Variant.Filled" OnClick="ConfirmDelete">Delete
        </MudButton>
    </div>
</div>

<div class="row">
    <div class="col-md">
        @if (allMenus.Any())
        {
            <SfTreeGrid DataSource="allMenus" @ref="dgMaintrv" IdMapping="@nameof(MenuDto.Id)"
                        ParentIdMapping="@nameof(MenuDto.ParentId)"
                        TreeColumnIndex="0" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 220px)">
                <TreeGridFilterSettings Type="Syncfusion.Blazor.TreeGrid.FilterType.Excel">
                    <TreeGridFilterColumns>
                        <TreeGridFilterColumn Field="@nameof(MenuDto.Active)" MatchCase="false"
                                              Operator="Operator.Equal" Predicate="and"
                                              Value="@yes"></TreeGridFilterColumn>
                    </TreeGridFilterColumns>
                </TreeGridFilterSettings>
                <TreeGridSortSettings>
                    <TreeGridSortColumns>
                        <TreeGridSortColumn Field="@nameof(MenuDto.ModuleName)"
                                            Direction="SortDirection.Ascending"></TreeGridSortColumn>
                        <TreeGridSortColumn Field="@nameof(MenuDto.SortOrder)"
                                            Direction="SortDirection.Ascending"></TreeGridSortColumn>
                    </TreeGridSortColumns>
                </TreeGridSortSettings>

                <TreeGridColumns>

                    <TreeGridColumn Width="300px" HeaderText="Name" Field="@nameof(MenuDto.Name)"></TreeGridColumn>
                    <TreeGridColumn Width="100px" HeaderText="Code" Format="000"
                                    Field="@nameof(MenuDto.Code)"></TreeGridColumn>
                    <TreeGridColumn Width="150px" HeaderText="Module"
                                    Field="@nameof(MenuDto.ModuleName)"></TreeGridColumn>
                    <TreeGridColumn Width="200px" HeaderText="Url" Field="@nameof(MenuDto.Url)"></TreeGridColumn>
                    <TreeGridColumn Width="200px" HeaderText="Url Target"
                                    Field="@nameof(MenuDto.URLTarget)"></TreeGridColumn>
                    <TreeGridColumn Width="100px" HeaderText="Menu Parent"
                                    Field="@nameof(MenuDto.ParentMenu)"></TreeGridColumn>
                    <TreeGridColumn Width="100px" HeaderText="Sort Order"
                                    Field="@nameof(MenuDto.SortOrder)"></TreeGridColumn>
                    <TreeGridColumn Width="100px" HeaderText="Active" Field="@nameof(MenuDto.Active)"></TreeGridColumn>
                </TreeGridColumns>
            </SfTreeGrid>

            @*<SfGrid Height="calc(100vh - 220px)" @ref="dgMain"
        DataSource="allMenus"
        AllowSelection="true"
        AllowFiltering="true"
        AllowSorting="true">
        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
        <GridColumns>
        <GridColumn AutoFit="true" HeaderText="Code" Format="000" Field="@nameof(MenuDTO.Code)"></GridColumn>
        <GridColumn AutoFit="true" HeaderText="Module" Field="@nameof(MenuDTO.ModuleName)"></GridColumn>
        <GridColumn HeaderText="Name" Field="@nameof(MenuDTO.Name)"></GridColumn>
        <GridColumn AutoFit="true" HeaderText="Url" Field="@nameof(MenuDTO.Url)"></GridColumn>
        <GridColumn AutoFit="true" HeaderText="Device" Field="@nameof(MenuDTO.Device)"></GridColumn>
        <GridColumn AutoFit="true" HeaderText="Active" Field="@nameof(MenuDTO.Active)"></GridColumn>
        </GridColumns>
        </SfGrid>*@
        }
    </div>
</div>

@code {

    private readonly string yes = "Yes";

    public class urltargets
    {
        public string ID { get; set; } = string.Empty;
        public string UrlTarget { get; set; } = string.Empty;
    }

    private readonly List<urltargets> utargets =
    [
        new() { ID = "_self", UrlTarget = "_self" },
        new() { ID = "_blank", UrlTarget = "_blank" },
        new() { ID = "iframe", UrlTarget = "iframe" }
    ];

    private List<string> MenuIconsList = new();

    private string searchText = "";
    private List<MenuDto> allMenus = new();
    private List<MenuDto> allParentMenus = new();
    private MenuDto obj = new();
    private string FormTitle = "Add Menu";

    private List<Modules> modules = new();
    //[CascadingParameter] public MainLayout Layout { get; set; }
    //[CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }


    SfDialog? dlgForm;

    // SfGrid<MenuDTO>? dgMain;
    SfTreeGrid<MenuDto>? dgMaintrv;

    //private List<Module>? module;
    private SfToast? toastObj;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            allMenus = await service.GetAllMenusAsync();
            modules = await service.getAllModules();
            MenuIconsList = await service.GetIconsList();
            allParentMenus = await service.GetAllActiveMenusAsync();
            //module = await service.getModuleForMenu();
            //Layout.PageCaption = "Menus";
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenCreateForm()
    {
        FormTitle = "Add Menu";
        try
        {
            obj = new MenuDto { IsActive = false };
            await dlgForm!.ShowAsync();
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }


    public async Task SaveData()
    {
        // var user = (await authenticationStateTask).User;
        // var op = "";
        // int userId = 0;
        // try
        // {
        //    userId = int.Parse(user.Claims.FirstOrDefault(c => c.Type == "Id").Value);

        // }
        // catch (Exception ex)
        // {
        //    op = ex.Message;
        // }

        // if (op != "")
        // {
        //    var tm = new ToastModel { Content = op, Title = "Error", ShowCloseButton = true, Timeout = 0 };
        //    await toastObj.ShowAsync(tm);
        //    return;
        // }

        try
        {
            var vm = await service.IsMenuValid(obj);
            if (vm == "OK")
            {
                var res = await service.SaveMenuAsync(obj, 0);
                allMenus = await service.GetAllMenusAsync();
                allParentMenus = await service.GetAllActiveMenusAsync();
                await dlgForm!.HideAsync();
            }
            else
            {
                var tm = new ToastModel { Content = vm, Title = "Error", ShowCloseButton = true, Timeout = 0 };
                await toastObj!.ShowAsync(tm);
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task OpenEditForm()
    {
        FormTitle = "Edit Menu";
        try
        {
            List<MenuDto> selectedRec = await dgMaintrv!.GetSelectedRecordsAsync();
            if (selectedRec is { Count: > 0 })
            {
                var id = selectedRec[0].Id;
                obj = await service.GetMenuAsync(id);
                IconUrl = IconHelper.GetIconByName(obj.Icon);
                await dlgForm!.ShowAsync();
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private async Task ConfirmDelete()
    {
        try
        {
            List<MenuDto> selectedRec = await dgMaintrv!.GetSelectedRecordsAsync();
            if (selectedRec is { Count: > 0 })
            {
                var conf = await DialogService.ConfirmAsync("Are you sure want to delete this record", "Confirm");
                var id = selectedRec[0].Id;
                if (conf)
                {
                    var res = await service.DeleteMenusAsync(id);
                    if (res == "OK")
                    {
                        allMenus = await service.GetAllMenusAsync();
                    }
                    else
                    {
                        var mm = new ToastModel { Content = "The entity cannot be deleted because it is being used by other entities.", Title = "Error", ShowCloseButton = true, Timeout = 0 };
                        await toastObj!.ShowAsync(mm);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var tm = new ToastModel { Content = ex.Message, Title = "Error", ShowCloseButton = true, Timeout = 0 };
            await toastObj!.ShowAsync(tm);
        }
    }

    private string IconUrl = "";

    private void OnIconChange(ChangeEventArgs<string, string> changeEventArgs)
    {
        IconUrl = IconHelper.GetIconByName(changeEventArgs.Value);
    }

}


<style>
    .e-grid td.e-active {
        background: #faa601 !important;
    }

</style>
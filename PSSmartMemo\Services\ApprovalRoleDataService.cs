using System.Xml;

namespace PSSmartMemo.Services;

public class ApprovalRoleDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    public Task<List<ApprovalRoleDto>> GetTemplateRolesAsync()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApproverRoles
            orderby a.MemoApproverRoleType, a.MemoApproverRoleTitle
            where a.MemoApproverRoleIsDel == false
            select new ApprovalRoleDto
            {
                Id = a.MemoApproverRoleId,
                Title = a.MemoApproverRoleTitle,
                Type = a.MemoApproverRoleType,
                UserId = a.MemoApproverRoleUserId,
                UserName = a.MemoApproverRoleUserName,
                UserEmail = a.MemoApproverRoleUserEmail,
                Code = a.MemoApproverRoleUserCode,
                Department = a.MemoApproverRoleUserDept,
                Designation = a.MemoApproverRoleUserDesg,
                IsActive = a.MemoApproverRoleIsActive,
                HCMCode = a.MemoApproverHcmcode
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<ApprovalRoleDto?> GetById(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApproverRoles
            where a.MemoApproverRoleId == id && a.MemoApproverRoleIsDel==false
            select new ApprovalRoleDto
            {
                Id = a.MemoApproverRoleId,
                Title = a.MemoApproverRoleTitle,
                Type = a.MemoApproverRoleType,
                UserId = a.MemoApproverRoleUserId,
                UserName = a.MemoApproverRoleUserName,
                UserEmail = a.MemoApproverRoleUserEmail,
                Code = a.MemoApproverRoleUserCode,
                Department = a.MemoApproverRoleUserDept,
                Designation = a.MemoApproverRoleUserDesg,
                IsActive = a.MemoApproverRoleIsActive,
                HCMCode = a.MemoApproverHcmcode
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<(ApprovalRoleDto, string)> Save(ApprovalRoleDto dto, string user)
    {
        var dc = contextFactory.CreateDbContext();
        // check duplicate role
        var isDuplicate = dc.MemoApproverRoles.Any(a => a.MemoApproverRoleId != dto.Id 
                                                        && a.MemoApproverRoleTitle == dto.Title 
                                                        && a.MemoApproverRoleType == dto.Type 
                                                        && a.MemoApproverRoleIsDel == false);
        if (isDuplicate)
        {
            return Task.FromResult((dto, "Role already exists"));
        }

        if (dto.Id == 0)
        {
            // create new role
            var newRole = new MemoApproverRole
            {
                MemoApproverRoleTitle = dto.Title,
                MemoApproverRoleType = dto.Type,
                MemoApproverRoleUserId = dto.UserId,
                MemoApproverRoleUserName = dto.UserName,
                MemoApproverRoleUserEmail = dto.UserEmail,
                MemoApproverRoleUserCode = dto.Code,
                MemoApproverRoleUserDept = dto.Department,
                MemoApproverRoleUserDesg = dto.Designation,
                MemoApproverRoleIsActive = dto.IsActive,
                MemoApproverRoleIsDel = false,
                MemoApproverRoleCreatedBy = user,
                MemoApproverRoleCreatedDate = DateTime.Now,
                MemoApproverHcmcode = dto.HCMCode
            };
            dc.MemoApproverRoles.Add(newRole);
            dc.SaveChanges();
            dto.Id = newRole.MemoApproverRoleId;
            return Task.FromResult((dto, "OK"));
        }

        // update existing role
        var role = dc.MemoApproverRoles.FirstOrDefault(c => c.MemoApproverRoleId == dto.Id &&
                                                            c.MemoApproverRoleIsDel == false);
        if (role == null)
        {
            return Task.FromResult((dto, "Role not found"));
        }
        role.MemoApproverRoleTitle = dto.Title;
        role.MemoApproverRoleType = dto.Type;
        role.MemoApproverRoleUserId = dto.UserId;
        role.MemoApproverRoleUserName = dto.UserName;
        role.MemoApproverRoleUserEmail = dto.UserEmail;
        role.MemoApproverRoleUserCode = dto.Code;
        role.MemoApproverRoleUserDept = dto.Department;
        role.MemoApproverRoleUserDesg = dto.Designation;
        role.MemoApproverRoleIsActive = dto.IsActive;
        role.MemoApproverRoleModifiedBy = user;
        role.MemoApproverRoleModifiedDate = DateTime.Now;
        role.MemoApproverHcmcode = dto.HCMCode;
        dc.SaveChanges();
        return Task.FromResult((dto, "OK"));
    }
    
    public Task<string> Delete(int id, string user)
    {
        var dc = contextFactory.CreateDbContext();
        var role = dc.MemoApproverRoles.FirstOrDefault(c => c.MemoApproverRoleId == id &&
                                                            c.MemoApproverRoleIsDel == false);
        if (role == null)
        {
            return Task.FromResult("Role not found");
        }
        role.MemoApproverRoleIsDel = true;
        role.MemoApproverRoleModifiedBy = user;
        role.MemoApproverRoleModifiedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }
}
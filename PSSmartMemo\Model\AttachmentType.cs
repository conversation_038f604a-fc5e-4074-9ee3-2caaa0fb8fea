﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class AttachmentType
{
    public int AttachmentTypeId { get; set; }

    public string AttachmentTypeTitle { get; set; }

    public int? MemoTypeId { get; set; }

    public DateTime? CreatedDate { get; set; }

    public string CreatedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string ModifiedBy { get; set; }

    public bool IsActive { get; set; }

    public string Description { get; set; }

    public virtual ICollection<MemoAttachment> MemoAttachments { get; set; } = new List<MemoAttachment>();

    public virtual MemoType MemoType { get; set; }
}
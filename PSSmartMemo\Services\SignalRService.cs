using Microsoft.AspNetCore.SignalR.Client;
using PSSmartMemo.Hubs;

namespace PSSmartMemo.Services;

public class SignalRService : IAsyncDisposable
{
    private readonly NavigationManager _navigationManager;
    private HubConnection? _hubConnection;
    
    public event Action<int, string, string, string>? OnMemoUpdate;
    public event Action<int, int, string, string>? OnApprovalUpdate;
    public event Action<string>? OnWorklistUpdate;
    public event Action<string>? OnDelegationUpdate;
    public event Action<string, string, string>? OnApproverNotification;
    
    public SignalRService(NavigationManager navigationManager)
    {
        _navigationManager = navigationManager;
    }
    
    public async Task InitializeAsync()
    {
        if (_hubConnection == null)
        {
            _hubConnection = new HubConnectionBuilder()
                .WithUrl(_navigationManager.ToAbsoluteUri("/memohub"))
                .WithAutomaticReconnect()
                .Build();
                
            _hubConnection.On<int, string, string, string>("ReceiveMemoUpdate", 
                (memoId, action, fromUser, toUser) => OnMemoUpdate?.Invoke(memoId, action, fromUser, toUser));
                
            _hubConnection.On<int, int, string, string>("ReceiveApprovalUpdate", 
                (memoId, approvalLogId, action, fromUser) => OnApprovalUpdate?.Invoke(memoId, approvalLogId, action, fromUser));
                
            _hubConnection.On<string>("ReceiveWorklistUpdate", 
                (userId) => OnWorklistUpdate?.Invoke(userId));
                
            _hubConnection.On<string>("ReceiveDelegationUpdate", 
                (userId) => OnDelegationUpdate?.Invoke(userId));
                
            _hubConnection.On<string, string, string>("ReceiveApproverNotification", 
                (userId, memoTitle, memoCode) => OnApproverNotification?.Invoke(userId, memoTitle, memoCode));
                
            await _hubConnection.StartAsync();
        }
    }
    
    public async Task SendMemoUpdateAsync(int memoId, string action, string fromUser, string toUser)
    {
        if (_hubConnection?.State == HubConnectionState.Connected)
        {
            await _hubConnection.SendAsync("SendMemoUpdate", memoId, action, fromUser, toUser);
        }
    }
    
    public async Task SendApprovalUpdateAsync(int memoId, int approvalLogId, string action, string fromUser)
    {
        if (_hubConnection?.State == HubConnectionState.Connected)
        {
            await _hubConnection.SendAsync("SendApprovalUpdate", memoId, approvalLogId, action, fromUser);
        }
    }
    
    public async Task SendWorklistUpdateAsync(string userId)
    {
        if (_hubConnection?.State == HubConnectionState.Connected)
        {
            await _hubConnection.SendAsync("SendWorklistUpdate", userId);
        }
    }
    
    public async Task SendDelegationUpdateAsync(string userId)
    {
        if (_hubConnection?.State == HubConnectionState.Connected)
        {
            await _hubConnection.SendAsync("SendDelegationUpdate", userId);
        }
    }
    
    public async Task NotifyNextApproverAsync(string userId, string memoTitle, string memoCode)
    {
        if (_hubConnection?.State == HubConnectionState.Connected)
        {
            await _hubConnection.SendAsync("NotifyNextApprover", userId, memoTitle, memoCode);
        }
    }
    
    public bool IsConnected => _hubConnection?.State == HubConnectionState.Connected;
    
    public async ValueTask DisposeAsync()
    {
        if (_hubConnection != null)
        {
            await _hubConnection.DisposeAsync();
        }
    }
}
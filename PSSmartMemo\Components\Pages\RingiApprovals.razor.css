.approval-page-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    padding: 16px;
    height: calc(100vh - 120px);
    overflow: auto;
}

.approval-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e0e0e0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-left h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
    color: #333;
}

.project-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    color: white;
}

.ringi-color {
    background: linear-gradient(135deg, #c43e1c, #8c2a12);
}

.approval-content {
    flex: 1;
}

.reference-cell {
    display: flex;
    align-items: center;
    gap: 6px;
}

.reference-icon {
    flex-shrink: 0;
}

.reference-link {
    color: #0078d4;
    text-decoration: none;
    font-weight: 500;
}

.reference-link:hover {
    text-decoration: underline;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    text-align: center;
}

.status-warning {
    background: linear-gradient(135deg, #ff9800, #e65100);
}

.status-success {
    background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.status-error {
    background: linear-gradient(135deg, #f44336, #b71c1c);
}

.status-info {
    background: linear-gradient(135deg, #2196f3, #0d47a1);
}

.status-default {
    background: linear-gradient(135deg, #9e9e9e, #616161);
}



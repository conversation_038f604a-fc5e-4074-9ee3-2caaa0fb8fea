﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class MemoAttachment
{
    public int MemoAttachmentId { get; set; }

    public string MemoAttachmentCode { get; set; }

    public string MemoAttachmentTitle { get; set; }

    public string MemoAttachmentDocName { get; set; }

    public string MemoAttachmentDocType { get; set; }

    public int? MemoAttachmentDocSizeMb { get; set; }

    public string MemoAttachmentFilePath { get; set; }

    public string MemoAttachmentDescription { get; set; }

    public string MemoAttachmentKeywords { get; set; }

    public int? MemoAttachmentSortOrder { get; set; }

    public bool MemoAttachmentIsActive { get; set; }

    public bool MemoAttachmentIsDel { get; set; }

    public DateTime? MemoAttachmentCreatedDate { get; set; }

    public string MemoAttachmentCreatedBy { get; set; }

    public DateTime? MemoAttachmentModifiedDate { get; set; }

    public string MemoAttachmentModifiedBy { get; set; }

    public int? MemoId { get; set; }

    public int? AttachmentTypeId { get; set; }

    public virtual AttachmentType AttachmentType { get; set; }

    public virtual Memo Memo { get; set; }
}
﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class FormStatus
{
    public int FormStatusId { get; set; }

    public int? FormId { get; set; }

    public int? FormTypeId { get; set; }

    public int? StatusId { get; set; }

    public string FormStatusUserId { get; set; }

    public string FormStatusUserEmail { get; set; }

    public string FormStatusUserRemarks { get; set; }

    public DateTime? FormStatusCreatedDate { get; set; }

    public string FormStatusCreatedBy { get; set; }

    public DateTime? FormStatusModifiedDate { get; set; }

    public string FormStatusModifiedBy { get; set; }

    public virtual FormType FormType { get; set; }

    public virtual ICollection<FormStatus> InverseStatus { get; set; } = new List<FormStatus>();

    public virtual FormStatus Status { get; set; }
}
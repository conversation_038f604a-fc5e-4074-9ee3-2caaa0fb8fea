@page "/memo-approvals"
@inject WorklistDataService Service
@attribute [Authorize]
@rendermode InteractiveServer
@inject NavigationManager NavMgr

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Project Dashboard" Url="/project-dashboard"></BreadcrumbItem>
        <BreadcrumbItem Text="Memo Approvals" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="approval-page-container">
    <div class="approval-header">
        <div class="header-left">
            <div class="project-icon memo-color">
                <MudIcon Icon="@Icons.Material.Filled.Email" Size="Size.Medium" />
            </div>
            <h2>Memo Approvals</h2>
        </div>
        <div class="header-actions">
            <MudButton 
                Variant="Variant.Outlined" 
                Color="Color.Primary" 
                StartIcon="@Icons.Material.Filled.ArrowBack"
                Size="Size.Small"
                OnClick="@(() => NavMgr.NavigateTo("/project-dashboard"))">
                Back to Dashboard
            </MudButton>
        </div>
    </div>

    <div class="approval-content">
        <PSSmartMemo.Components.Pages.Worklist.MyWorklist />
    </div>
</div>

@code {
    [CascadingParameter] private Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";

    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
        }
    }
}

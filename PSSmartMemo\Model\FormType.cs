﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class FormType
{
    public int FormTypeId { get; set; }

    public string FormTypeCode { get; set; }

    public string FormTypeTitle { get; set; }

    public DateTime? FormTypeCreatedDate { get; set; }

    public string FormTypeCreatedBy { get; set; }

    public DateTime? FormTypeModifiedDate { get; set; }

    public string FormTypeModifiedBy { get; set; }

    public virtual ICollection<FormStatus> FormStatuses { get; set; } = new List<FormStatus>();

    public virtual ICollection<FormTypeStatus> FormTypeStatuses { get; set; } = new List<FormTypeStatus>();

    public virtual ICollection<MemoTemplate> MemoTemplates { get; set; } = new List<MemoTemplate>();

    public virtual ICollection<Memo> Memos { get; set; } = new List<Memo>();
}
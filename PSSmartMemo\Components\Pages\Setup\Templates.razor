﻿@page "/setup/templates"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inject NavigationManager NavMgr
@inject TemplateDataService Service
@inject IJSRuntime js
@inject AdminDataService adminService

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Templates" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<SfToast @ref="toastObj"></SfToast>

<SfDialog @ref="dlgPreview" Width="800px" Height="90vh" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true"
          Visible="false">
    <DialogTemplates>
        <Header>Template Preview</Header>
        <Content>
            <TemplatePreview TemplateId="templateId"></TemplatePreview>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Add Role Assignment Dialog -->
<SfDialog @ref="dlgFormRole" Visible="false" Width="540px" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true">
    <DialogTemplates>
        <Header>Assign Roles</Header>
        <Content>
            <div class="row" style="display:flex">
                <MudText Typo="Typo.caption">Selected Template: <b>@selectedTemplate.TemplateTitle</b></MudText>
            </div>
            <div style="height:10px;"></div>
            <div class="row" style="text-align:right;">
                <SfCheckBox @bind-Checked="isChecked" Label="Select All" @onchange="selectAll">Select All</SfCheckBox>
            </div>
            <div style="height:10px;"></div>
            <div class="row">
                <SfGrid @ref="dgMainRole" Height="250px" DataSource="allRole" AllowSelection="true"
                        AllowFiltering="true" AllowSorting="true" Width="100%">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn AutoFit="true" Visible="false" HeaderText="Id"
                                    Field="@nameof(RoleDTO.Id)"></GridColumn>
                        <GridColumn Field="@nameof(RoleDTO.isSelect)" HeaderTextAlign="TextAlign.Center"
                                    HeaderText="Selected" AutoFit="true" ClipMode="ClipMode.EllipsisWithTooltip">
                            <Template>
                                @{
                                    if (context is RoleDTO ctx)
                                    {
                                        <div>
                                            <div style="text-align: center;">
                                                <SfCheckBox @bind-Checked="@ctx.isSelect"></SfCheckBox>
                                            </div>
                                        </div>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn AutoFit="true" HeaderText="Roles" Field="@nameof(RoleDTO.Name)"></GridColumn>

                    </GridColumns>
                </SfGrid>
            </div>
            <div class="page-panel">
                <div>
                    <MudButton Size="Size.Small" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Save"
                               Variant="Variant.Filled" OnClick="SaveTemplateRole">Save
                    </MudButton>
                    <MudButton Size="Size.Small" Color="Color.Success" StartIcon="@Icons.Material.Filled.Cancel"
                               Variant="Variant.Filled" OnClick="() => dlgFormRole!.HideAsync()">Cancel
                    </MudButton>
                </div>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<div style="display:flex;gap:10px;align-items:center" class="mb-2">
    <MudText Typo="Typo.h5">Templates</MudText>
    @*<SfButton CssClass="e-primary" IconCss="e-icons e-file-new"
              OnClick="OpenNewTemplateForm">
        Create
    </SfButton>*@
    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
               OnClick="OpenNewTemplateForm"
               StartIcon="@Icons.Material.Filled.Add">Create</MudButton>
    
    <div style="flex:1"></div>
    <div class="col-md" style="display: flex;gap: 10px;min-width:535px">
        @*<SfButton CssClass="e-primary" IconCss="e-icons e-print-layout"
                  OnClick="OpenPreviewTemplate">
            Preview
        </SfButton>*@
        <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Info"
                   OnClick="OpenPreviewTemplate"
                   StartIcon="@Icons.Material.Filled.Preview">Preview</MudButton>
        @*<SfButton CssClass="e-success" IconCss="e-icons e-user"
                  OnClick="OpenAssignRoles">
            Assign Roles
        </SfButton>*@
        <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Success"
                   OnClick="OpenAssignRoles"
                   StartIcon="@Icons.Material.Filled.Group">Assign Role</MudButton>
        @*<SfButton CssClass="e-info" IconCss="e-icons e-edit"
                  OnClick="OpenEditForm">
            Edit
        </SfButton>*@
        <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Dark"
                   OnClick="OpenEditForm"
                   StartIcon="@Icons.Material.Filled.EditNote">Edit</MudButton>
        @*<SfButton CssClass="e-warning" IconCss="e-icons e-trash"
        OnClick="ConfirmDeleteTemplate">Delete</SfButton>*@
        <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Error"
                   OnClick="ConfirmDeleteTemplate"
                   StartIcon="@Icons.Material.Filled.Delete">Delete</MudButton>
        @*<SfButton CssClass="e-secondary" IconCss="e-icons e-save"
                  OnClick="ConfirmPublishTemplate">
            Publish
        </SfButton>*@
        <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Warning"
                   OnClick="ConfirmPublishTemplate"
                   StartIcon="@Icons.Material.Filled.Publish">Publish</MudButton>
    </div>
</div>

<SfGrid @ref="dgMain" DataSource="@TemplateList" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true"
        Height="calc(100vh - 190px)">
    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn HeaderText="Template" Field="@nameof(TemplateDto.TemplateTitle)" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Type" Field="@nameof(TemplateDto.MemoType)" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Status" Field="@nameof(TemplateDto.Status)" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Created Date" Field="@nameof(TemplateDto.CreatedDate)" AutoFit="true" Format="d-MMM-yyyy h:mm"></GridColumn>
        <GridColumn HeaderText="Last Update Date" Field="@nameof(TemplateDto.ModifiedDate)" AutoFit="true" Format="d-MMM-yyyy h:mm"></GridColumn>
        <GridColumn HeaderText="Template Status" Field="@nameof(TemplateDto.TemplateStatus)"
                    AutoFit="true"></GridColumn>
        
    </GridColumns>
</SfGrid>

@code {

    //cascade auth state provider
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private List<TemplateDto> TemplateList { get; set; } = new();
    private SfToast? toastObj;
    private List<RoleDTO> allRole = new();
    private TemplateDto selectedTemplate = new();
    private SfDialog? dlgFormRole;
    private SfGrid<RoleDTO>? dgMainRole;
    bool isChecked;
    private SfDialog? dlgPreview;
    private int templateId = 0;
    private SfGrid<TemplateDto>? dgMain;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        // get user id by auth state if authenticated
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
        }

        TemplateList = await Service.GetAll();
    }

    private async Task ConfirmDeleteTemplate()
    {
        templateId = 0;
        if (dgMain != null && dgMain.SelectedRecords.Any())
        {
            templateId= dgMain.SelectedRecords.First().TemplateId;
        }
        if(templateId==0) return;
        
        // confirm delete by javascript confirm dialog
        var result = await js.InvokeAsync<bool>("confirm", "Are you sure you want to delete this template?");
        if (result)
        {
            var msg = await Service.DeleteById(templateId, userId);
            if (msg == "OK")
            {
                TemplateList = await Service.GetAll();
            }
            else
            {
                await toastObj!.ShowAsync(new ToastModel
                {
                    Content = msg,
                    CssClass = "e-toast-danger",
                    Icon = "e-error toast-icons",
                    ShowCloseButton = true,
                    ShowProgressBar = true
                });
            }
        }
    }


    private void OpenNewTemplateForm(MouseEventArgs obj)
    {
        NavMgr.NavigateTo("/setup/templates/new");
    }

    private async Task ConfirmPublishTemplate()
    {
        templateId = 0;
        if (dgMain != null && dgMain.SelectedRecords.Any())
        {
            templateId= dgMain.SelectedRecords.First().TemplateId;
        }
        // confirm publish by javascript confirm dialog
        var result = await js.InvokeAsync<bool>("confirm", "Are you sure you want to publish this template?");
        if (result)
        {
            var msg = await Service.PublishTemplate(templateId, userId);
            if (msg == "OK")
            {
                TemplateList = await Service.GetAll();

                await toastObj!.ShowAsync(new ToastModel
                {
                    Content = "Template published successfully",
                    CssClass = "e-toast-success",
                    Icon = "e-success toast-icons",
                    ShowCloseButton = true,
                    ShowProgressBar = true
                });
            }
            else
            {
                await toastObj!.ShowAsync(new ToastModel
                {
                    Content = msg,
                    CssClass = "e-toast-danger",
                    Icon = "e-error toast-icons",
                    ShowCloseButton = true,
                    ShowProgressBar = true
                });
            }
        }
    }


    private async Task selectAll()
    {
        try
        {
            foreach (var item in allRole)
            {
                item.isSelect = isChecked;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            await toastObj!.ShowAsync(new ToastModel
            {
                Content = ex.Message,
                CssClass = "e-toast-danger",
                Icon = "e-error toast-icons",
                ShowCloseButton = true,
                ShowProgressBar = true
            });
        }
    }

    private async Task OpenAssignRoles()
    {
        templateId = 0;
        if (dgMain != null && dgMain.SelectedRecords.Any())
        {
            templateId= dgMain.SelectedRecords.First().TemplateId;
        }

        if (templateId==0) return;
        
        try
        {
            //selectedTemplate = template;
            allRole = await Service.GetAllRolesAsyncWithSelectedTemplate(templateId);
            await dlgFormRole!.ShowAsync();
        }
        catch (Exception ex)
        {
            await toastObj!.ShowAsync(new ToastModel
            {
                Content = ex.Message,
                CssClass = "e-toast-danger",
                Icon = "e-error toast-icons",
                ShowCloseButton = true,
                ShowProgressBar = true
            });
        }
    }

    private async Task SaveTemplateRole()
    {
        try
        {
            await Service.SaveTemplateRoles(templateId, allRole, userId);
            await toastObj!.ShowAsync(new ToastModel
            {
                Content = "Roles assigned successfully",
                CssClass = "e-toast-success",
                Icon = "e-success toast-icons",
                ShowCloseButton = true,
                ShowProgressBar = true
            });
            await dlgFormRole!.HideAsync();
        }
        catch (Exception ex)
        {
            await toastObj!.ShowAsync(new ToastModel
            {
                Content = ex.Message,
                CssClass = "e-toast-danger",
                Icon = "e-error toast-icons",
                ShowCloseButton = true,
                ShowProgressBar = true
            });
        }
    }

    private async Task OpenPreviewTemplate()
    {
        if (dgMain != null && dgMain.SelectedRecords.Any())
        {
            templateId= dgMain.SelectedRecords.First().TemplateId;
            await dlgPreview!.ShowAsync();
        }
        
        
    }

    private void OpenEditForm()
    {
        templateId = 0;
        if (dgMain != null && dgMain.SelectedRecords.Any())
        {
            templateId= dgMain.SelectedRecords.First().TemplateId;
        }

        if (templateId == 0) return;
        NavMgr.NavigateTo("/setup/templates/edit/" + templateId);
    }

}

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PSSmartMemo.Model;

public class DelegationDto
{
    public int Id { get; set; }

    public string FromUserId { get; set; }

    public string FromUserName { get; set; }

    public string ToUserId { get; set; }

    public string ToUserName { get; set; }
    // not required but max length is 500
    public string Comments { get; set; }
    // required
    public string Type { get; set; }
    // required
    public DateTime? DateFrom { get; set; }
    // required and date to must be greater than date from

    public DateTime? DateTo { get; set; }

    public bool IsActive { get; set; } = true;
    public string Status => IsActive ? "Active" : "Inactive";
} 
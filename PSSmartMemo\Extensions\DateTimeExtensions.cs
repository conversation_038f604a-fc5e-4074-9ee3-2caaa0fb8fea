﻿namespace PSSmartMemo.Extensions;

public static class DateTimeExtensions
{
    public static string GetTimeSpanString(this DateTime dateTime)
    {
        TimeSpan timeSpan = DateTime.Now - dateTime;

        if (timeSpan.TotalDays < 1)
        {
            if (timeSpan.TotalHours < 1)
            {
                return $"{timeSpan.Minutes} minutes"; //Or Seconds if you want more granularity
            }
            return $"{timeSpan.Hours} hours";
        }
        else if (timeSpan.TotalDays < 30) // Approximately a month
        {
            return $"{timeSpan.Days} day{(timeSpan.Days > 1 ? "s" : "")} and {timeSpan.Hours} hour{(timeSpan.Hours > 1 ? "s" : "")}";
        }
        else
        {
            int months = timeSpan.Days / 30;
            int remainingDays = timeSpan.Days % 30;  //Days beyond full months
            return $"{months} month{(months > 1 ? "s" : "")} and {remainingDays} day{(remainingDays > 1 ? "s" : "")}";
        }
    }


    public static string GetPreciseTimeSpanString(this DateTime dateTime)
    {
        TimeSpan timeSpan = DateTime.Now - dateTime;

        if (timeSpan.TotalDays < 1)
        {
            if (timeSpan.TotalHours < 1)
            {
                return $"{timeSpan.Minutes} minute{(timeSpan.Minutes > 1 ? "s" : "")} and {timeSpan.Seconds} second{(timeSpan.Seconds > 1 ? "s" : "")}"; //Or Seconds if you want more granularity
            }
            return $"{timeSpan.Hours} hour{(timeSpan.Hours > 1 ? "s" : "")} and {timeSpan.Minutes} minute{(timeSpan.Minutes > 1 ? "s" : "")}";
        }
        else if (timeSpan.TotalDays < 30) // Approximately a month
        {
            return $"{timeSpan.Days} day{(timeSpan.Days > 1 ? "s" : "")}, {timeSpan.Hours} hour{(timeSpan.Hours > 1 ? "s" : "")}, {timeSpan.Minutes} minute{(timeSpan.Minutes > 1 ? "s" : "")} and {timeSpan.Seconds} second{(timeSpan.Seconds > 1 ? "s" : "")}";
        }
        else
        {
            int months = timeSpan.Days / 30;
            int remainingDays = timeSpan.Days % 30;  //Days beyond full months
            return $"{months} month{(months > 1 ? "s" : "")}, {remainingDays} day{(remainingDays > 1 ? "s" : "")}, {timeSpan.Hours} hour{(timeSpan.Hours > 1 ? "s" : "")}, {timeSpan.Minutes} minute{(timeSpan.Minutes > 1 ? "s" : "")} and {timeSpan.Seconds} second{(timeSpan.Seconds > 1 ? "s" : "")}";
        }
    }
}

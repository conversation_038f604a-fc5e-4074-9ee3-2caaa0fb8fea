﻿namespace PSSmartMemo.Services;

public class AttachmentTypeDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    public Task<List<AttachmentTypeDto>> GetAll()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.AttachmentTypes
            orderby a.AttachmentTypeTitle
            select new AttachmentTypeDto
            {
                Id = a.AttachmentTypeId,
                Title = a.AttachmentTypeTitle,
                Description = a.Description,
                IsActive = a.IsActive,
                MemoTypeId = a.MemoTypeId,
                MemoType = a.MemoType == null ? "" : a.MemoType.MemoTypeName
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<(AttachmentTypeDto, string)> Save(AttachmentTypeDto entity, string user)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.AttachmentTypes
            where a.AttachmentTypeId != entity.Id &&
                  a.AttachmentTypeTitle == entity.Title 
            select a).Any();

        if (q)
            return Task.FromResult((entity, "Attachment Type Already exists"));

        if (entity.Id == 0)
        {
            var at = new AttachmentType
            {
                AttachmentTypeTitle = entity.Title,
                Description = entity.Description,
                IsActive = entity.IsActive,
                MemoTypeId = entity.MemoTypeId,
                CreatedBy = user,
                CreatedDate = DateTime.Now
            };
            dc.AttachmentTypes.Add(at);
            dc.SaveChanges();
            entity.Id = at.AttachmentTypeId;
            return Task.FromResult((entity, "OK"));
        }
        else
        {
            var at = dc.AttachmentTypes.Find(entity.Id);
            if (at == null)
                return Task.FromResult((entity, "Attachment Type not found"));

            at.AttachmentTypeTitle = entity.Title;
            at.Description = entity.Description;
            at.IsActive = entity.IsActive;
            at.MemoTypeId = entity.MemoTypeId;
            at.ModifiedBy = user;
            at.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult((entity, "OK"));
        }
    }

    public Task<string> DeleteById(int id, string user)
    {
        var dc = contextFactory.CreateDbContext();
        var at = dc.AttachmentTypes.Find(id);
        if (at == null)
            return Task.FromResult("Attachment Type not found");
        dc.AttachmentTypes.Remove(at);
        dc.SaveChanges();
        return Task.FromResult("OK");
    }
}

using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class MemoSectionDto
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public int? MemoId { get; set; }
    public Guid? SectionId { get; set; }
    public string? Section { get; set; }
    public string? Content { get; set; }
    public string? RefContent { get; set; }
    public string? ContentHtml { get; set; }
    public bool MemoIsActive {  get; set; }
    public Guid MemoTemplateSectionId { get; set; }          
    public string? TemplateSectionTitle {  get; set; }
    public string? TemplateContentHtml { get; set; }
    public bool IsRequired { get; set; }
    public int? SectionSortOrder { get; set; } = 0;
    public bool isOpen { get; set; } = false;
    public bool IgnoreSection { get; set; } = false;
    public string? Placeholder { get; set; }
    public bool IsWaterMark { get; set; } = true;
}
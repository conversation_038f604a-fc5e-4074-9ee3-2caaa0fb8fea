﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class MemoTemplateSection
{
    public Guid MemoTemplateSectionId { get; set; }

    public string MemoTemplateSectionCode { get; set; }

    public string MemoTemplateSectionType { get; set; }

    public string MemoTemplateSectionDataType { get; set; }

    public string MemoTemplateSectionFormControl { get; set; }

    public string MemoTemplateSectionTitle { get; set; }

    public string MemoTemplateSectionContentText { get; set; }

    public string MemoTemplateSectionContentHtml { get; set; }

    public int? MemoTemplateSectionSortOrder { get; set; }

    public string MemoTemplateSectionMendatoryType { get; set; }

    public bool MemoTemplateSectionIsActive { get; set; }

    public bool MemoTemplateSectionIsDel { get; set; }

    public DateTime? MemoTemplateSectionCreatedDate { get; set; }

    public string MemoTemplateSectionCreatedBy { get; set; }

    public DateTime? MemoTemplateSectionModifiedDate { get; set; }

    public string MemoTemplateSectionModifiedBy { get; set; }

    public int? MemoTemplateId { get; set; }

    public bool MemoTemplateSectionIsRequired { get; set; }

    public bool MemoTemplateSectionIsWaterMark { get; set; }

    public virtual ICollection<MemoSection> MemoSections { get; set; } = new List<MemoSection>();

    public virtual MemoTemplate MemoTemplate { get; set; }
}
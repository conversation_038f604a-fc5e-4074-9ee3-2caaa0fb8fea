﻿@page "/worklist"

@inject WorklistDataService Service
@attribute [Authorize]
@rendermode InteractiveServer
@using FilterType = Syncfusion.Blazor.Grids.FilterType
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Approval list" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>
<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Approval list</MudText>

</div>
<MyWorklist />
@code {

}

@using MudBlazor

<style>
    .approval-timeline {
        position: relative;
        padding: 10px 0;
    }

    .timeline-item {
        display: flex;
        margin-bottom: 12px;
        position: relative;
    }

    .timeline-marker {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
        color: white;
    }

    .timeline-content {
        flex-grow: 1;
        background: #f0f2f5;
        border-radius: 6px;
        padding: 10px 12px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
    }

    .timeline-title {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .action-type {
        font-weight: 600;
        font-size: 0.9rem;
    }

    .timestamp {
        color: #666;
        font-size: 0.8rem;
    }

    .user-flow {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #666;
        font-size: 0.8rem;
    }

    .timeline-comments {
        background: white;
        padding: 4px 5px;
        border-radius: 4px;
        margin-top: 6px;
        border: 1px solid #dee2e6;
        font-size: 0.85rem;
    }

    .approved {
        background-color: #28a745;
    }

    .rejected {
        background-color: #dc3545;
    }

    .object {
        background-color: #ffc107;
    }

    .query {
        background-color: #17a2b8;
    }

    .forward {
        background-color: #6c757d;
    }

    .skip {
        background-color: #6c757d;
    }

    .reply {
        background-color: #007bff;
    }
</style>

<div class="approval-timeline">
    @foreach (var log in ApprovalLogs)
    {
        <div class="timeline-item">
            <div class="timeline-marker @GetActionColor(log.Action)">
                <MudIcon Icon="@GetActionIcon(log.Action)" />
            </div>
            <div class="timeline-content">
                <div class="timeline-header">
                    <div class="timeline-title">
                        <span class="action-type">@log.Action</span>
                        <span class="timestamp">@log.ActionDateTime!.Value.ToString("d MMM, yyyy h:mm")</span>
                    </div>
                    <div class="user-flow">
                        <span class="from-user">@log.FromUser</span>
                        <MudIcon Icon="@Icons.Material.Filled.ArrowForward" Size="Size.Small" />
                        <span class="to-user">@log.ToUser</span>
                    </div>
                </div>
                @if (!string.IsNullOrEmpty(log.Comments))
                {
                    <div class="timeline-comments">
                        <MudText Typo="Typo.body2">@log.Comments</MudText>
                    </div>
                }
            </div>
        </div>
    }
</div>

@code {
    [Parameter]
    public List<MemoApprovalLogDto> ApprovalLogs { get; set; } = new();

    private string GetActionColor(string action)
    {
        return action.ToLower() switch
        {
            "approved" => "approved",
            "rejected" => "rejected",
            "object" => "object",
            "query" => "query",
            "forward" => "forward",
            "skip" => "skip",
            "reply" => "reply",
            _ => "forward"
        };
    }

    private string GetActionIcon(string action)
    {
        return action.ToLower() switch
        {
            "approved" => Icons.Material.Filled.Check,
            "rejected" => Icons.Material.Filled.Close,
            "object" => Icons.Material.Filled.Warning,
            "query" => Icons.Material.Filled.Help,
            "forward" => Icons.Material.Filled.Forward,
            "skip" => Icons.Material.Filled.SkipNext,
            "reply" => Icons.Material.Filled.Reply,
            _ => Icons.Material.Filled.Info
        };
    }
} 
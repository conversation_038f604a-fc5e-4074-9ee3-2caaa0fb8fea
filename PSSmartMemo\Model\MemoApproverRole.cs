﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class MemoApproverRole
{
    public int MemoApproverRoleId { get; set; }

    public string MemoApproverRoleTitle { get; set; }

    /// <summary>
    /// Dynamic | Fixed | Generic
    /// </summary>
    public string MemoApproverRoleType { get; set; }

    public string MemoApproverRoleUserId { get; set; }

    public string MemoApproverRoleUserCode { get; set; }

    public string MemoApproverRoleUserName { get; set; }

    public string MemoApproverRoleUserDept { get; set; }

    public string MemoApproverRoleUserDesg { get; set; }

    public string MemoApproverRoleUserEmail { get; set; }

    public string MemoApproverRoleDesc { get; set; }

    public DateTime MemoApproverRoleCreatedDate { get; set; }

    public string MemoApproverRoleCreatedBy { get; set; }

    public DateTime? MemoApproverRoleModifiedDate { get; set; }

    public string MemoApproverRoleModifiedBy { get; set; }

    public bool MemoApproverRoleIsActive { get; set; }

    public bool MemoApproverRoleIsDel { get; set; }

    public string MemoApproverHcmcode { get; set; }

    public virtual ICollection<MemoApprover> MemoApprovers { get; set; } = new List<MemoApprover>();

    public virtual ICollection<MemoTemplateApprover> MemoTemplateApprovers { get; set; } = new List<MemoTemplateApprover>();
}
﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class MemoApprover
{
    public int MemoApproverId { get; set; }

    public string MemoApproverCode { get; set; }

    public string MemoApproverTitle { get; set; }

    public string MemoApproverUserId { get; set; }

    public string MemoApproverUserEmail { get; set; }

    public string MemoApproverUserName { get; set; }

    public string MemoApproverUserDetail { get; set; }

    public string MemoApproverDescription { get; set; }

    public int? MemoApproverSortOrder { get; set; }

    public bool MemoApproverIsActive { get; set; }

    public bool MemoApproverIsDel { get; set; }

    public DateTime? MemoApproverCreatedDate { get; set; }

    public string MemoApproverCreatedBy { get; set; }

    public DateTime? MemoApproverModifiedDate { get; set; }

    public string MemoApproverModifiedBy { get; set; }

    public int? MemoId { get; set; }

    public string MemoAllowType { get; set; }

    public int? MemoApproverRoleId { get; set; }

    public Guid Id { get; set; }

    public bool MemoApproverSkipped { get; set; }

    public virtual Memo Memo { get; set; }

    public virtual ICollection<MemoApprovalLog> MemoApprovalLogFromApprovers { get; set; } = new List<MemoApprovalLog>();

    public virtual ICollection<MemoApprovalLog> MemoApprovalLogToApprovers { get; set; } = new List<MemoApprovalLog>();

    public virtual MemoApproverRole MemoApproverRole { get; set; }
}
using Microsoft.AspNetCore.SignalR;

namespace PSSmartMemo.Hubs;

public class MemoHub : Hub
{
    public async Task SendMemoUpdate(int memoId, string action, string fromUser, string toUser)
    {
        await Clients.All.SendAsync("ReceiveMemoUpdate", memoId, action, fromUser, toUser);
    }
    
    public async Task SendApprovalUpdate(int memoId, int approvalLogId, string action, string fromUser)
    {
        await Clients.All.SendAsync("ReceiveApprovalUpdate", memoId, approvalLogId, action, fromUser);
    }
    
    public async Task SendWorklistUpdate(string userId)
    {
        await Clients.All.SendAsync("ReceiveWorklistUpdate", userId);
    }
    
    public async Task SendDelegationUpdate(string userId)
    {
        await Clients.All.SendAsync("ReceiveDelegationUpdate", userId);
    }
    
    public async Task NotifyNextApprover(string userId, string memoTitle, string memoCode)
    {
        await Clients.All.SendAsync("ReceiveApproverNotification", userId, memoTitle, memoCode);
    }
}

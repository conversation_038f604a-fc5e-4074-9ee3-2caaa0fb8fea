﻿using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class MemApproverFormDto
{
    public int MemoApproverId { get; set; }
    public Guid Id { get; set; }
    public Guid MyProperty { get; set; }
    [Required, <PERSON><PERSON>ength(50)]
    public string? UserId { get; set; }
    [Required,MaxLength(300)]
    public string? Email { get; set; }
    [Required, MaxLength(100)]
    public string? Name { get; set; }
    [Required, MaxLength(100)]
    public string? Designation { get; set; }

}

using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class ApprovalRoleDto
{
    public int Id { get; set; }
    [Required]
    [MaxLength(500)]
    public string? Title { get; set; }
    [Required]
    [MaxLength(50)]
    public string? Type { get; set; }
    [MaxLength(300)]
    public string? UserId { get; set; }
    [MaxLength(300)]
    public string? UserName { get; set; }
    [MaxLength(300)]
    public string? UserEmail { get; set; }
    [MaxLength(50)]
    public string? Code { get; set; }
    [MaxLength(300)]
    public string? Department { get; set; }
    [MaxLength(300)]
    public string? Designation { get; set; }
    public bool IsActive { get; set; }
    public string? HCMCode { get; set; }
}
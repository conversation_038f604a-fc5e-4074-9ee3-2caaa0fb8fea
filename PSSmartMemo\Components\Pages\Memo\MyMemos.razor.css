.e-plus-icon::before {
    content: '\e805';
}

.watermarked {
    position: relative;
    padding: 20px;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
}

.watermarked::before {
    content: "Watermark Text"; /* The watermark text */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 4em;
    color: rgba(0, 0, 0, 0.1); /* Semi-transparent black color */
    pointer-events: none; /* Prevents the watermark from interfering with interactions */
    z-index: -1; /* Ensures the watermark stays behind other content */
    user-select: none; /* Disables text selection for watermark */
}

.chip {
    display: inline-block;
    padding: 0 25px;
    height: 50px;
    font-size: 16px;
    line-height: 50px;
    border-radius: 25px;
    background-color: #f1f1f1;
}
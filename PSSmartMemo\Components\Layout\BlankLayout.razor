@inherits LayoutComponentBase
@inject IWebHostEnvironment env
@inject AppDataService service
@inject CorporateService cService
@inject AuthenticationStateProvider AuthenticationStateProvider
<style>
    .mmii {
    }

    .mmii button {
        margin: 0;
        padding: 0;
    }
</style>
<MudThemeProvider/>
<MudDialogProvider/>
<MudSnackbarProvider/>
<Syncfusion.Blazor.Popups.SfDialogProvider/>
<MudPopoverProvider />
<div class="">@Body</div>

@code {

    private string GetUserFullName(string userId)
    {
        var fullName = service.GetUserFullName(userId).Result;
        return fullName;
    }


    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        var userId = "";
        if (user.Identity is { Name: not null })
        {
            userId = user.Identity.Name;
        }


    }

}

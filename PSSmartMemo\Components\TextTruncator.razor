﻿@if (isTextTruncated)
{
    <span>@truncatedText</span>
    <button class="nav-link" @onclick="ToggleText">More</button>
}
else
{
    <span>@FullText</span>
    <button class="nav-link" @onclick="ToggleText">Less</button>
}

@code {
    [Parameter] public string FullText { get; set; }
    private string truncatedText = "";
    private bool isTextTruncated = true;

    [Parameter] public int TruncateLength { get; set; } = 100;

    protected override void OnInitialized()
    {
        if (FullText.Length > TruncateLength)
        {
            truncatedText = FullText.Substring(0, TruncateLength) + "...";
        }
        else
        {
            truncatedText = FullText;
            isTextTruncated = false;
        }
    }

    private void ToggleText()
    {
        isTextTruncated = !isTextTruncated;
    }

}

<style>
    .m-link {
        color: #013582;
        text-decoration: none;
    }

    .m-link:hover {
        text-decoration: underline;
    }
</style>
@page "/ringi-approvals"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Syncfusion.Blazor.Grids
@inject CorpApprovalDataService CorpAprService
@attribute [Authorize]
@rendermode InteractiveServer
@inject NavigationManager NavMgr

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Project Dashboard" Url="/project-dashboard"></BreadcrumbItem>
        <BreadcrumbItem Text="RINGI Approvals" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="approval-page-container">
    <div class="approval-header">
        <div class="header-left">
            <div class="project-icon ringi-color">
                <MudIcon Icon="@Icons.Material.Filled.Description" Size="Size.Medium" />
            </div>
            <h2>RINGI Approvals</h2>
        </div>
        <div class="header-actions">
            <MudButton
                Variant="Variant.Outlined"
                Color="Color.Primary"
                StartIcon="@Icons.Material.Filled.ArrowBack"
                Size="Size.Small"
                OnClick="@(() => NavMgr.NavigateTo("/project-dashboard"))">
                Back to Dashboard
            </MudButton>
        </div>
    </div>

    <div class="approval-content">
        <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
            <MudTabPanel Text="Direct Approvals">
                <SfGrid DataSource="@ringiListDirect" AllowFiltering="true" AllowSorting="true">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn HeaderText="Reference" Width="160">
                            <Template Context="context">
                                @{
                                    if (context is RINGIListItemDto item)
                                    {
                                        <div class="reference-cell">
                                            <MudIcon Icon="@Icons.Material.Filled.Description" Color="Color.Error" Size="Size.Small" Class="reference-icon" />
                                            <a class="reference-link" href="@item.previewUrl" target="_blank">@item.Reference</a>
                                        </div>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn HeaderText="Control No" Field="@nameof(RINGIListItemDto.ControlNo)" Width="120"></GridColumn>
                        <GridColumn HeaderText="Start Date" Field="@nameof(RINGIListItemDto.StartDate)" Width="100"></GridColumn>
                        <GridColumn HeaderText="Request Type" Field="@nameof(RINGIListItemDto.RequestType)" Width="130"></GridColumn>
                        <GridColumn HeaderText="Forward By" Field="@nameof(RINGIListItemDto.ForwardBy)" Width="130"></GridColumn>
                        <GridColumn HeaderText="Status" Field="@nameof(RINGIListItemDto.Status)" Width="100">
                            <Template Context="context">
                                @{
                                    if (context is RINGIListItemDto item)
                                    {
                                        <span class="status-badge @GetStatusColorClass(item.Status)">
                                            @item.Status
                                        </span>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn HeaderText="Pending Since" Field="@nameof(RINGIListItemDto.PendingSince)" Width="120"></GridColumn>
                    </GridColumns>
                </SfGrid>
            </MudTabPanel>
            <MudTabPanel Text="Indirect Approvals">
                <SfGrid DataSource="@ringiListIndirect" AllowFiltering="true" AllowSorting="true">
                    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn HeaderText="Reference" Width="160">
                            <Template Context="context">
                                @{
                                    if (context is RINGIListItemDto item)
                                    {
                                        <div class="reference-cell">
                                            <MudIcon Icon="@Icons.Material.Filled.Description" Color="Color.Error" Size="Size.Small" Class="reference-icon" />
                                            <a class="reference-link" href="@item.previewUrl" target="_blank">@item.Reference</a>
                                        </div>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn HeaderText="Control No" Field="@nameof(RINGIListItemDto.ControlNo)" Width="120"></GridColumn>
                        <GridColumn HeaderText="Start Date" Field="@nameof(RINGIListItemDto.StartDate)" Width="100"></GridColumn>
                        <GridColumn HeaderText="Request Type" Field="@nameof(RINGIListItemDto.RequestType)" Width="130"></GridColumn>
                        <GridColumn HeaderText="Forward By" Field="@nameof(RINGIListItemDto.ForwardBy)" Width="130"></GridColumn>
                        <GridColumn HeaderText="Status" Field="@nameof(RINGIListItemDto.Status)" Width="100">
                            <Template Context="context">
                                @{
                                    if (context is RINGIListItemDto item)
                                    {
                                        <span class="status-badge @GetStatusColorClass(item.Status)">
                                            @item.Status
                                        </span>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn HeaderText="Pending Since" Field="@nameof(RINGIListItemDto.PendingSince)" Width="120"></GridColumn>
                    </GridColumns>
                </SfGrid>
            </MudTabPanel>
        </MudTabs>
    </div>
</div>

@code {
    [CascadingParameter] private Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private List<RINGIListItemDto> ringiListDirect = new();
    private List<RINGIListItemDto> ringiListIndirect = new();

    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
                await LoadData();
            }
        }
    }

    private async Task LoadData()
    {
        ringiListDirect = await CorpAprService.GetRINGIListAsync(userId, 1);
        ringiListIndirect = await CorpAprService.GetRINGIListAsync(userId, 2);
        StateHasChanged();
    }

    private string GetStatusColorClass(string status)
    {
        return status?.ToLower() switch
        {
            "pending" => "status-warning",
            "approved" => "status-success",
            "rejected" => "status-error",
            "completed" => "status-info",
            _ => "status-default"
        };
    }
}



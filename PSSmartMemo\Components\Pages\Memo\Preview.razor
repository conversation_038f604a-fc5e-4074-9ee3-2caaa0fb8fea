﻿@layout PreviewLayout
@page "/memos/{MemoId:int}/preview"
@using PSSmartMemo.Components.Layout
@inject IJSRuntime JS
@inject MemoDataService Service
@inject WorklistDataService wlService
@rendermode InteractiveServer
@using SelectPdf
@inject IWebHostEnvironment WebHostEnvironment
@inject NavigationManager NavigationManager


<div class="tmp-preview-container" id="printable-content">
    <div class="tmp-preview-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 40px; padding: 10px;">

            <div class="memo-header-info">
                <div><img src="images/logo.png" style="height: 20px" alt="logo" /></div>

                <span class="memo-code">@memoObj?.MemoCode</span>
                <h1 class="memo-title">@(memoObj?.MemoTitle?.ToUpper())</h1>
                <div>

                <div class="memo-initiated">Initiated By: @memoObj?.InitiatedBy</div> 
                <div class="memo-initiated">Initiated On: @memoObj?.MemoCreatedDate?.ToString("d MMM, yyyy")</div> 
                </div>
                
                @*<span class="memo-type">@memoObj?.MemoTypeStr</span>*@
                
            </div>
            
            <table style="widows:300px">
                <tr><td style="width:120px"><b>Department:</b>&nbsp;</td><td>@memoObj?.Department</td></tr>
                <tr><td><b>Division:</b>&nbsp;</td><td>@memoObj?.Division</td></tr>
            </table>
                

            
        </div>
    </div>

    <div class="tmp-preview-sections-wrapper">
        @if (memoObj?.MemoSections != null)
        {
            @foreach (var sec in memoObj.MemoSections.Where(c=>c.MemoSectionIgnored==false))
            {
                <div class="tmp-preview-section-block">
                    <div class="tmp-preview-section-header">
                        <h3 class="tmp-preview-section-title">@sec.MemoSectionTitle</h3>
                    </div>
                    <div class="tmp-preview-section-content">
                        @((MarkupString)sec.MemoSectionContentHtml!)
                    </div>
                </div>
            }
        }
    </div>

    <div class="page-block">
        @if (attachments.Any())
        {
            <div class="tmp-preview-section-block">
                <div class="tmp-preview-section-header">
                    <h3 class="tmp-preview-section-title">Attachments</h3>
                </div>
                <table class="approver-table">
                    <thead>
                        <tr>
                            <th>File Name</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Description</th>
                            <th class="no-print">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var file in attachments)
                        {
                            <tr>
                                <td>@file.Name</td>
                                <td>@file.AttachmentType</td>
                                <td>@FormatFileSize(Convert.ToInt64(file.Size))</td>
                                <td>@file.Description</td>
                                <td class="no-print">
                                    <MudLink Href="@file.Path" Target="_blank" Disabled="@(file.Path.StartsWith("http"))">
                                        <MudIcon Icon="@Icons.Material.Filled.Download" />
                                        Download
                                    </MudLink>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }

        <div class="tmp-preview-section-block">
            <div class="tmp-preview-section-header">
                <h3 class="tmp-preview-section-title">Approvers</h3>
            </div>
            <PSSmartMemo.Components.Shared.ApproversPopup MemoId="MemoId"></PSSmartMemo.Components.Shared.ApproversPopup>
            @*<table class="approver-table">
                <thead>
                    <tr>
                        <th>Date and Time</th>
                        <th>From User</th>
                        <th>Action</th>
                        <th>To User</th>
                        <th>Comments</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var ii in approvalLogs)
                    {
                        <tr>
                            <td>@ii.ActionDateTime!.Value.ToString("d MMM, yyyy h:mm tt")</td>
                            <td>@ii.FromUser</td>
                            <td>@ii.Action</td>
                            <td>@ii.ToUser</td>
                            <td>@ii.Comments</td>
                        </tr>
                    }
                </tbody>
            </table>*@
        </div>
    </div>

    <div class="action-buttons">
        <button @onclick="Print" class="print-button" title="Print Memo">
            <MudIcon Icon="@Icons.Material.Filled.Print" />
        </button>
        @*<button @onclick="GeneratePDF" class="print-button pdf-button" title="Generate PDF">
            <MudIcon Icon="@Icons.Material.Filled.PictureAsPdf" />
        </button>*@
    </div>
</div>

@code {
    [Parameter] public int MemoId { get; set; }
    private MemoDto? memoObj;

    protected override async Task OnParametersSetAsync()
    {
        memoObj = await Service.GetMemoPreview(MemoId);
        approvalLogs = await wlService.GetMemoApprovalLogs(MemoId);
        attachments = await Service.GetMemoAttachments(MemoId);
        //(DivisionName, DepartmentName) = await Service.GetUserDivisionAndDeptByMemoId(MemoId);
    }

    private async Task Print()
    {
        await JS.InvokeVoidAsync("window.print");
    }

    private async Task GeneratePDF()
    {
        try
        {
            // Create PDF converter object
            HtmlToPdf converter = new HtmlToPdf();

            // Configure converter options
            converter.Options.PdfPageSize = SelectPdf.PdfPageSize.A4;
            converter.Options.PdfPageOrientation = PdfPageOrientation.Portrait;
            converter.Options.MarginLeft = 20;   // reduced from 40
            converter.Options.MarginRight = 20;  // reduced from 40
            converter.Options.MarginTop = 20;    // reduced from 40
            converter.Options.MarginBottom = 20; // reduced from 40

            // Add these settings for better table handling
            //converter.Options.WebKitViewportSize = "1024x0";
            converter.Options.MinPageLoadTime = 2;
            
            // Enable JavaScript and wait for page load
            converter.Options.JavaScriptEnabled = true;
            
            // Set rendering options
            converter.Options.RenderingEngine = RenderingEngine.WebKit;
            
            // Get base URL for resolving relative paths
            var baseUrl = NavigationManager.BaseUri.TrimEnd('/');

            // Get the HTML content
            var htmlContent = await JS.InvokeAsync<string>("getElementContent", "printable-content");

            // Add CSS styles and structure
            var cssPath = System.IO.Path.Combine(WebHostEnvironment.WebRootPath, "app.css");
            var css = await File.ReadAllTextAsync(cssPath);
            
            var fullHtml = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <base href='{baseUrl}/'>
    <style>
        {css}
        @page {{
            size: A4;
            margin: 20pt;
        }}
        body {{
            margin: 0;
            padding: 0;
            font-size: 14px !important;
            font-family: Arial, sans-serif !important;
        }}
        
        h1 {{
            font-size: 24px !important;
            margin-bottom: 15px !important;
        }}
        
        h2 {{
            font-size: 20px !important;
            margin-bottom: 12px !important;
        }}
        
        h3 {{
            font-size: 18px !important;
            margin-bottom: 10px !important;
        }}
        
        p {{
            font-size: 14px !important;
            line-height: 1.4 !important;
            margin-bottom: 8px !important;
        }}
        
        table {{
            width: 100% !important;
            max-width: 100% !important;
            margin: 0 !important;
            page-break-inside: auto !important;
            font-size: 13px !important;
        }}
        
        th {{
            font-size: 13px !important;
            font-weight: bold !important;
            padding: 6px !important;
            background-color: #f0f0f0 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }}
        
        td {{
            font-size: 13px !important;
            padding: 6px !important;
            line-height: 1.3 !important;
        }}
        
        label {{
            font-size: 14px !important;
            font-weight: 500 !important;
        }}
        
        li {{
            font-size: 14px !important;
            line-height: 1.4 !important;
            margin-bottom: 4px !important;
        }}
        
        * {{
            box-shadow: none !important;
            text-shadow: none !important;
            transition: none !important;
            transform: none !important;
        }}
        
        .tmp-preview-container {{
            padding: 15px;
            max-width: 100% !important;
            margin: 0 !important;
        }}

        .memo-header-info {{
            display: flex;
            flex-direction: column;
            gap: 8px;
        }}

        .memo-code {{
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }}

        .memo-title {{
            font-size: 24px;
            margin: 0;
            color: #333;
        }}

        .memo-initiated {{
            font-size: 13px;
            color: #666;
            font-style: italic;
        }}
        
        .tmp-preview-section-content {{
            width: 100% !important;
            max-width: 100% !important;
            overflow: hidden !important;
            margin-bottom: 15px !important;
        }}
        
        .approver-table {{
            border-collapse: collapse !important;
            width: 100% !important;
            margin-bottom: 15px !important;
        }}
        
        .approver-table td,
        .approver-table th {{
            border: 1px solid #000 !important;
            padding: 6px !important;
        }}
        
        .no-print,
        .action-buttons,
        .navigation-controls {{
            display: none !important;
        }}
        
        img {{
            max-width: 100% !important;
            height: auto !important;
        }}
    </style>
</head>
<body>
    <div class='tmp-preview-container'>
        {htmlContent}
    </div>
</body>
</html>";

            // Generate unique filename
            var fileName = $"Memo_{MemoId}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
            var tempDir = System.IO.Path.Combine(WebHostEnvironment.WebRootPath, "temp");
            var filePath = System.IO.Path.Combine(tempDir, fileName);
            
            // Ensure temp directory exists
            Directory.CreateDirectory(tempDir);

            // Convert HTML to PDF
            var doc = converter.ConvertHtmlString(fullHtml, baseUrl);

            // Save PDF
            doc.Save(filePath);
            doc.Close();

            // Trigger download in browser
            await JS.InvokeVoidAsync("window.open", $"/temp/{fileName}", "_blank");
        }
        catch (Exception ex)
        {
            // Log the error
            Console.WriteLine($"PDF Generation Error: {ex.Message}");
            
            // Show error to user
            await JS.InvokeVoidAsync("alert", "Error generating PDF. Please try again.");
        }
    }
    private List<MemoApprovalLogDto> approvalLogs = new();
    private List<MemoAttachmentDto> attachments = new();
    // private string? DepartmentName;
    // private string? DivisionName;

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        return $"{size:0.##} {sizes[order]}";
    }
}

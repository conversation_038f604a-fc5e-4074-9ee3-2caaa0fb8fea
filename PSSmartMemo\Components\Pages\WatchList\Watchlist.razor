@page "/watchlist"
@inject WatchListDataService Service
@attribute [Authorize]
@rendermode InteractiveServer
@using FilterType = Syncfusion.Blazor.Grids.FilterType

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Watch list" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Watch list</MudText>
</div>

<div class="row">
    <div class="col-md">
        <SfGrid DataSource="watchlist" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 180px)" AllowTextWrap="true">
            <GridEvents TValue="MemoDto" RowDataBound="OnRowDataBind"></GridEvents>
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn HeaderText="Code" AutoFit="true">
                    <Template Context="kk">
                        @{
                            if (kk is MemoDto mm)
                            {
                                <SfButton CssClass="e-link" OnClick="@(() => ViewMemo((mm)))">@mm.MemoCode</SfButton>
                            }
                        }
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Memo" Field="@nameof(MemoDto.MemoTitle)" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Type" Field="@nameof(MemoDto.MemoTyeShort)" AutoFit="true"></GridColumn>

                <!-- Initiator Information -->
                <GridColumn HeaderText="Initiated By" Field="@nameof(MemoDto.MemoCreatedBy)" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Initiated Date" Field="@nameof(MemoDto.MemoCreatedDate)" AutoFit="true">
                    <Template Context="context">
                        @{
                            if (context is MemoDto memo && memo.MemoCreatedDate.HasValue)
                            {
                                @memo.MemoCreatedDate.Value.ToString("dd MMM yyyy HH:mm")
                            }
                        }
                    </Template>
                </GridColumn>

                <!-- Last Action Information -->
                <GridColumn HeaderText="Last Action" AutoFit="true" Field="@nameof(MemoDto.LastAction)"></GridColumn>
                <GridColumn HeaderText="Last Action By" Field="@nameof(MemoDto.LastActionBy)" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Delegation Approval" Field="@nameof(MemoDto.DelegatinApproval)" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Last Action Date" Field="@nameof(MemoDto.LastActionDate)" AutoFit="true">
                    <Template Context="context">
                        @{
                            if (context is MemoDto memo && memo.LastActionDate.HasValue)
                            {
                                @memo.LastActionDate.Value.ToString("dd MMM yyyy HH:mm")
                            }
                        }
                    </Template>
                </GridColumn>

                <!-- Approval Required Information -->
                <GridColumn HeaderText="Approval Required From" Field="@nameof(MemoDto.RequiredApprovalFrom)" AutoFit="true"></GridColumn>
                <GridColumn HeaderText="Pending Since" Field="@nameof(MemoDto.PendingSince)" AutoFit="true"></GridColumn>

            </GridColumns>
        </SfGrid>
    </div>
</div>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private List<MemoDto> watchlist { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
            }
        }

        watchlist = await Service.GetMyWatchList(userId);
        watchlist = watchlist.OrderByDescending(c => c.LastActionDate).ToList();
    }

    public void ViewMemo(MemoDto memo)
    {
        NavMgr.NavigateTo($"/watchlist/viewmemo/{memo.MemoId}");
    }

    private void OnRowDataBind(RowDataBoundEventArgs<MemoDto> obj)
    {
        if (obj.Data.LastAction == "Approved")
        {
            obj.Row.AddStyle(["background-color:#B7FFB7 "]);
        }
        else if (obj.Data.LastAction == "Object")
        {
            obj.Row.AddStyle(["background-color:#FF6699 "]);
        }
        else if (obj.Data.LastAction == "Reply")
        {
            obj.Row.AddStyle(["background-color:#ffd7c4 "]);
        }
    }
}
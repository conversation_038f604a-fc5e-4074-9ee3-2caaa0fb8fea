-- First, find the Setup menu ID
DECLARE @SetupMenuId INT = (SELECT Id FROM Menus WHERE Name = 'Setup' AND MenuParentId = 0);

-- Get the next available sort order for the Setup menu's children
DECLARE @NextSortOrder INT = (SELECT ISNULL(MAX(SortOrder), 0) + 1 FROM Menus WHERE MenuParentId = @SetupMenuId);

-- Add the Delegations menu item
INSERT INTO Menus (Name, Url, MenuParentId, ModuleId, IsActive, CreatedDate, CreatedBy, SortOrder, Code)
SELECT 
    'Delegations',
    '/setup/delegations',
    @SetupMenuId,
    (SELECT Id FROM Modules WHERE ModuleTitle = 'Setup'),
    1,
    GETDATE(),
    1,
    @NextSortOrder,
    '000';

-- Add the menu to the admin role
INSERT INTO RoleMenus (RoleId, MenuId, IsActive, CreatedDate, CreatedBy)
SELECT 
    (SELECT Id FROM Roles WHERE Name = 'Admin'),
    SCOPE_IDENTITY(),
    1,
    GETDATE(),
    1; 
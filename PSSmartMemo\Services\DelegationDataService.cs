using PSSmartMemo.Model;

namespace PSSmartMemo.Services;

public class DelegationDataService(IDbContextFactory<ApplicationDbContext> contextFactory) : IDataService<DelegationDto>
{
    public Task<List<DelegationDto>> GetAll()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.Delegations
            join fromUser in dc.Users on a.FromUserId equals fromUser.UserId
            join toUser in dc.Users on a.ToUserId equals toUser.UserId
            orderby a.DateFrom descending
            select new DelegationDto
            {
                Id = a.Id,
                FromUserId = a.FromUserId,
                FromUserName = fromUser.Name,
                ToUserId = a.ToUserId,
                ToUserName = toUser.Name,
                Comments = a.Comments,
                Type = a.Type,
                DateFrom = a.DateFrom,
                DateTo = a.DateTo,
                IsActive = a.IsActive
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<DelegationDto?> GetById(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.Delegations
            where a.Id == id
            select new DelegationDto
            {
                Id = a.Id,
                FromUserId = a.FromUserId,
                ToUserId = a.ToUserId,
                Comments = a.Comments,
                Type = a.Type,
                DateFrom = a.DateFrom,
                DateTo = a.DateTo,
                IsActive = a.IsActive
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<(DelegationDto, string)> Save(DelegationDto entity, string user)
    {
        var dc = contextFactory.CreateDbContext();

        // Check if delegation already exists for the same users and date range
        var existingDelegation = (from a in dc.Delegations
            where a.Id != entity.Id &&
                  a.FromUserId == entity.FromUserId &&
                  a.ToUserId == entity.ToUserId &&
                  a.Type == entity.Type &&
                  ((a.DateFrom <= entity.DateFrom && a.DateTo >= entity.DateFrom) ||
                   (a.DateFrom <= entity.DateTo && a.DateTo >= entity.DateTo) ||
                   (a.DateFrom >= entity.DateFrom && a.DateTo <= entity.DateTo))
            select a).Any();

        if (existingDelegation)
            return Task.FromResult((entity, "Delegation already exists for the specified date range"));

        if (entity.Id == 0)
        {
            var delegation = new Delegation
            {
                FromUserId = entity.FromUserId,
                ToUserId = entity.ToUserId,
                Comments = entity.Comments,
                Type = entity.Type,
                DateFrom = entity.DateFrom,
                DateTo = entity.DateTo,
                IsActive = entity.IsActive,
                CraatedBy = user,
                CreatedDate = DateTime.Now
            };
            dc.Delegations.Add(delegation);
            dc.SaveChanges();
            entity.Id = delegation.Id;
            return Task.FromResult((entity, "OK"));
        }
        else
        {
            var delegation = dc.Delegations.Find(entity.Id);
            if (delegation == null)
                return Task.FromResult((entity, "Delegation not found"));

            delegation.FromUserId = entity.FromUserId;
            delegation.ToUserId = entity.ToUserId;
            delegation.Comments = entity.Comments;
            delegation.Type = entity.Type;
            delegation.DateFrom = entity.DateFrom;
            delegation.DateTo = entity.DateTo;
            delegation.IsActive = entity.IsActive;
            delegation.ModifiedBy = user;
            delegation.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult((entity, "OK"));
        }
    }

    public Task<string> DeleteById(int id, string user)
    {
        var dc = contextFactory.CreateDbContext();
        var delegation = dc.Delegations.Find(id);
        if (delegation == null)
            return Task.FromResult("Delegation not found");

        try
        {
            delegation.IsActive = false;
            delegation.ModifiedBy = user;
            delegation.ModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<List<DelegationDto>> GetDelegationsAssignedToUser(string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.Delegations
            join fromUser in dc.Users on a.FromUserId equals fromUser.UserId
            join toUser in dc.Users on a.ToUserId equals toUser.UserId
            where a.ToUserId.ToLower() == userId.ToLower()
                  && a.IsActive == true
                  && a.DateFrom <= DateTime.Today
                  && a.DateTo >= DateTime.Today
            orderby a.DateFrom descending
            select new DelegationDto
            {
                Id = a.Id,
                FromUserId = a.FromUserId,
                FromUserName = fromUser.Name,
                ToUserId = a.ToUserId,
                ToUserName = toUser.Name,
                Comments = a.Comments,
                Type = a.Type,
                DateFrom = a.DateFrom,
                DateTo = a.DateTo,
                IsActive = a.IsActive
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<string>> GetDelegatedFromUserIds(string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var delegatedFromUserIds = (from a in dc.Delegations
            where a.ToUserId.ToLower() == userId.ToLower()
                  && a.IsActive == true
                  && a.DateFrom <= DateTime.Now
                  && a.DateTo >= DateTime.Now
            select a.FromUserId).Distinct().ToList();
        return Task.FromResult(delegatedFromUserIds);
    }
}
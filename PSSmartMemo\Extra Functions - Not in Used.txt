private async Task PerformAction4(string action)
{
    IsObjectFormOpen = false;
    IsQueryFormOpen = false;
    if (action == "open-object")
    {
        toApproverList = await Service.GetUpperApprovers(_logId);
        IsObjectFormOpen = true;
        return;
    }
    if (action == "open-query")
    {
        toApproverList = await Service.GetUpperApprovers(_logId);
        IsQueryFormOpen = true;
        return;
    }
    action = action.ToLower();

    if (action is "approved" or "rejected")
    {
        string msg = await Service.PerformAction(action, _logId, -1, currentState.DraftComments);
        if (msg == "OK")
        {

            canForward = await Service.CanForward(_logId);
            canClose = await Service.CanClose(_logId);
            IsObjectFormOpen = await Service.IsObjectFormOpen(_logId);
            IsQueryFormOpen = await Service.IsQueryFormOpen(_logId);
            //currentComment = currentState.DraftComments;
            if (IsObjectFormOpen || IsQueryFormOpen)
            {
                var cc = await Service.GetLogById(_logId);
                toApproverList = await Service.GetUpperApprovers(_logId);
                toApproverId = cc.DraftToUserId;
            }
            var tm = new ToastModel { Content = "Record has saved successfully", Title = "Success", ShowProgressBar = true, ShowCloseButton = true, Timeout = 5000 };
            await toastObj.ShowAsync(tm);
        }
        else
        {
            // show error message
            var tm = new ToastModel() { Content = msg, Title = "Error", ShowCloseButton = true, ShowProgressBar = true, Timeout = 5000 };
            await toastObj.ShowAsync(tm);
            canForward = false;
            canClose = false;
        }
        //canForward = await Service.CanForward(_logId);
        //canClose = await Service.CanClose(_logId);

    }
    else if (action is "query" or "object")
    {
        string msg = await Service.PerformAction(action, _logId, toApproverId, currentState.DraftComments);
        if (msg == "OK")
        {
            canForward = await Service.CanForward(_logId);
            canClose = await Service.CanClose(_logId);
            IsObjectFormOpen = await Service.IsObjectFormOpen(_logId);
            IsQueryFormOpen = await Service.IsQueryFormOpen(_logId);
            //currentComment = currentState.DraftComments;
            if (IsObjectFormOpen || IsQueryFormOpen)
            {
                var cc = await Service.GetLogById(_logId);
                toApproverList = await Service.GetUpperApprovers(_logId);
                toApproverId = cc.DraftToUserId;
            }
            var tm = new ToastModel { Content = "Record has saved successfully", Title = "Success", ShowProgressBar = true, ShowCloseButton = true, Timeout = 5000 };
            await toastObj.ShowAsync(tm);
        }
        else
        {
            // show error message
            var tm = new ToastModel() { Content = msg, Title = "Error", ShowCloseButton = true, ShowProgressBar = true, Timeout = 5000 };
            await toastObj.ShowAsync(tm);
            canForward = false;
            canClose = false;
        }
    }
    else if (action is "forward")
    {
        string msg = await Service.PerformForward(_logId);
        if (msg == "OK")
        {
            NavMgr.NavigateTo("/worklist");
        }
    }
    else if (action is "close")
    {
        //string msg = await Service.PerformClose(_logId, currentState.DraftComments);
        string msg = "OK";
        if (msg == "OK")
        {
            NavMgr.NavigateTo("/worklist");
        }
    }
    else if (action is "reply")
    {
        string msg = await Service.PerformReply(_logId, currentState.DraftComments);
        if (msg == "OK")
        {
            NavMgr.NavigateTo("/approval-list");
        }
    }
}


public Task<string> PerformAction(string action, int logId, int? toApproverId, string comments)
    {
        
        action = action.ToLower();
        
            var log = dc.MemoApprovalLogs.FirstOrDefault(c => c.MemoApprovalLogId == logId);
        if (log != null)
        {
            log.DraftComments = comments;
            if (action == "approved") log.DraftActionId = 2;
            if (action == "rejected") log.DraftActionId = 3;
            if (action is "object") 
            {
                log.DraftActionId = 4;
                log.DraftToUserId = toApproverId;
            }
            if (action is "query")
            {
                log.DraftActionId = 5;
                log.DraftToUserId = toApproverId;
            }
            if(action is "rejected")
            {
                log.ToApproverId = log.FromApproverId;
            }
            
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        return Task.FromResult("Log entry not found");
        
        
        
    }
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    //"DefaultConnection": "Data Source=NABIL-PC\\SQLEXPRESS;Initial Catalog=PSSmartMemo;trustServerCertificate=True;integrated security=true"
    "DefaultConnection": "Data Source=(local);Initial Catalog=PSSmartMemoPS;trustServerCertificate=True;integrated security=true"
  },
  // EPPlus Configuration
  "EPPlus": {
    "LicenseContext": "NonCommercial"
  },
  "apibaseurl": "http://localhost:5238/",
  "empsearchurl": "http://localhost:5238/",
  "sapapiurl": "http://ps2-pop-ci.stan.suzuki:50000/igwj/odata/SAP/ZGET_CIPEMPINFO_CDS/ZGET_CIPEMPINFO(Pempcode='{0}')/Set?j_username=POUSER&j_password=Psmc@12345",
  "sapapiurlAllEmp": "http://localhost:5238/api/employees"
  //"sapapiurlAllEmp": "http://ps2-pop-ci.stan.suzuki:50000/igwj/odata/SAP/ZGET_CIPALLEMP_CDS/ZGet_CIPALLEMP?j_username=POUSER&j_password=Psmc@12345"
}

﻿.item {
    background: unset;
}

.item .card-outer {
    display: flex;
    padding: 5px;
    width: 100%;
}

.tag {
    height: 16px;
    background-color: greenyellow;
    padding: 5px;
    border: solid 1px green;
    border-radius: 5px;
}


.card-heading {
    color: #013582;
    font-size: 1.1rem;
}

.card-container-pic {
    width: 100%;
    height: 305px;
    overflow-x: auto;
    display: flex;
    gap: 20px;
}

.card-container-pic smart-carousel.smart-container,
.card-container-pic smart-carousel > .smart-container {
    height: 303px;
}


.card-container {
    width: 100%;
    height: 470px;
    overflow-x: auto;
    display: flex;
    gap: 20px;
}

.card {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--mud-elevation-1);
    min-width: 31%;
    display: flex !important;
    cursor: pointer;
    height: 100%;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    overflow: hidden;
    max-height: 447px;
}

.img-only {
    height: 300px !important;
    min-width: 100% !important;
    object-fit: cover !important;
    padding: 2px;
    overflow: hidden;
    border-radius: 15px;

}

.img-only-container {
    height: 320px !important;
}

.card-image {
    width: 100%;
    height: 250px !important;
    object-fit: cover;
    border-radius: 15px 15px 0 0;
}

.card-content {
    padding: 15px;
    background-color: white;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    max-height: 300px;
}

.card-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: #013582;
    margin-bottom: 1.2rem;
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;

}


.card-description {
    margin-bottom: 1rem;
    font-size: 12px;
    color: black;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    /* adjust the number of lines to your liking */
    -webkit-box-orient: vertical;
    height: 52px;
    /* adjust the height to your liking */
    color: #7F7F7F;
    min-height: 52px;
    max-height: 52px;
}

.card-date {
    font-size: 12px;
    color: #999;
}

.card-date span {
    font-weight: bold;
}

.popup h2 {
    font-size: 18px;
}

.popup p {
    margin-bottom: 10px;
}

.card .header {
    display: flex !important;
    padding: 10px;
    border-bottom: 1px solid gray;
    gap: 10px;
    justify-content: start;
    align-items: center;
}

.card .header img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 50%;
}

.card .header .user {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.card .header .user p {
    font-weight: 600;
    color: #013582;
}

.card .header .user span {
    color: gray;
    font-size: 10px;
}
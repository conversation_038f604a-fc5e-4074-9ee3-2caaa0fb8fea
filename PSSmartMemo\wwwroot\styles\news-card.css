﻿.newscard {
    background-color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: var(--mud-elevation-1);
    height: 400px;
    overflow-y: auto;

}

.newscard .news {
    display: flex;
    gap: 20px;
    cursor: pointer;
}

/*.newscard {
    width: 100%;
    padding: 10px 15px;
    display: flex;
    overflow-y: auto;
    flex-direction: column;
    cursor: pointer;
    background-color:white;
}

.news {
    display: flex;
    gap: 30px;
    align-items: center;
    border-bottom: solid 1px gainsboro;
}*/

.news h3 {
    font-weight: bold;
    font-size: 16px;
    color: #013582;
    margin-bottom: 10px;
}

.news p {
    font-size: 12px;
    color: #7f7f7f;
    margin-bottom: 10px;
}

.news img {
    width: 200px;
    height: 120px;
    border-radius: 10px;
    object-fit: cover;
}

.tag {
    height: 16px;
    background-color: greenyellow;
    padding: 5px;
    border: solid 1px green;
    border-radius: 5px;
}

.news .news-content {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

}
﻿namespace PSSmartMemo.Services;

public class MemoTypeDataService(IDbContextFactory<ApplicationDbContext> contextFactory) : IDataService<MemoTypeDto>
{
    public Task<string> DeleteById(int id, string user)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoTypes
            where a.MemoTypeId == id
            select a).FirstOrDefault();
        if (q == null)
            return Task.FromResult("Record not found");
        try
        {
            q.MemoTypeIsDel = true;
            q.MemoTypeModifiedDate = DateTime.Now;
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<List<MemoTypeDto>> GetAll()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoTypes
            orderby a.MemoTypeName
            where a.MemoTypeIsDel == false
            select new MemoTypeDto
            {
                Id = a.MemoTypeId,
                Title = a.MemoTypeName,
                Code = a.MemoTypeCode,
                IsActive = a.MemoTypeIsActive ?? false
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<MemoTypeDto?> GetById(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoTypes
            where a.MemoTypeId == id &&
                  a.MemoTypeIsDel == false
            select new MemoTypeDto
            {
                Id = a.MemoTypeId,
                Title = a.MemoTypeName,
                Code = a.MemoTypeCode,
                IsActive = a.MemoTypeIsActive ?? false
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<(MemoTypeDto, string)> Save(MemoTypeDto entity, string user)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoTypes
            where a.MemoTypeId != entity.Id && a.MemoTypeName == entity.Title
            select a).Any();
        if (q)
            return Task.FromResult((entity, "Memo Type Already exists"));
        if (entity.Id == 0)
        {
            var mt = new MemoType
            {
                MemoTypeName = entity.Title,
                MemoTypeCode = entity.Code,
                MemoTypeCreatedBy = user,
                MemoTypeCreatedDate = DateTime.Now,
                MemoTypeIsActive = entity.IsActive,
                MemoTypeIsDel = false
            };
            dc.MemoTypes.Add(mt);
            dc.SaveChanges();
            entity.Id = mt.MemoTypeId;
            return Task.FromResult((entity, "OK"));
        }
        else
        {
            var mt = dc.MemoTypes.FirstOrDefault(c => c.MemoTypeId == entity.Id);
            if (mt == null)
                return Task.FromResult((entity, "Record not found"));
            mt.MemoTypeName = entity.Title;
            mt.MemoTypeCode = entity.Code;
            mt.MemoTypeModifiedBy = user;
            mt.MemoTypeModifiedDate = DateTime.Now;
            mt.MemoTypeIsActive = entity.IsActive;
            dc.SaveChanges();
            return Task.FromResult((entity, "OK"));
        }
    }
}
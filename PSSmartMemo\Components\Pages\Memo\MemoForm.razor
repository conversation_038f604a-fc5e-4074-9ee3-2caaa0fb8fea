﻿@page "/memos/create"
@page "/memos/edit/{id:int}"
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Memos" Url="/memos"></BreadcrumbItem>
        <BreadcrumbItem Text="Memo Detail" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>
<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Memo Detail</MudText>


</div>


@code {
    [Parameter] public int? id { get; set; }
    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
    }
}

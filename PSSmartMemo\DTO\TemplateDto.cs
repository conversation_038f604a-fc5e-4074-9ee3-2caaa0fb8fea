using System.Collections;
using System.ComponentModel.DataAnnotations;
using Syncfusion.Blazor.TreeGrid.Internal;

namespace PSSmartMemo.DTO;

public class TemplateDto
{
    public DateTime? CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }

    public int TemplateId { get; set; }
    [Required]
    [StringLength(500)]
    public string? TemplateTitle { get; set; }
    
    public bool IsActive { get; set; }
    public string Status => IsActive ? "Active" : "Inactive";
    public byte TemplateStatusId { get; set; }
    public string? TemplateStatus { get; set; }
    public int? TemplateTypeId { get; set; }
    [ValidateComplexType]
    public List<TemplateSectionDto> Sections { get; set; } = new List<TemplateSectionDto>();
    [Required(ErrorMessage = "Memo Type is required")]
    public int? MemoTypeId { get; set; }
    [Required(ErrorMessage = "Form Type is required")]
    
    public string? MemoTemplateCode { get; set; }

    public string? MemoTemplateVersion { get; set; }
    public bool MemoTemplateAttachmentAllowed { get; set; }
    public int? MemoTemplateApproverCountAllowed { get; set; }
    public string? MemoTemplatePrefixCode { get; set; }
    public int? MemoTemplateAttachmentFileCountAllowed { get; set; }
    public double? MemoTemplateAttachmentPerFileSizeMbAllowed { get; set; }
    public string? MemoType { get; set; }
    public List<MemoTemplateApproverDto> Approvers { get; set; } = new();
    
}
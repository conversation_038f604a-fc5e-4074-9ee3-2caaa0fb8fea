﻿@page "/admin/syncusersdata"
@inject CorporateService Service
@using Syncfusion.Blazor.Grids
<div style=" display: flex; align-content: space-between; justify-content: space-between; align-items: center;" class="mb-2">
    <h2>Current Employees</h2>
    <SfButton OnClick="SyncData" CssClass="e-primary">Sync Employees Data</SfButton>
</div>
<SfGrid DataSource="@CurrentEmployees" AllowPaging="true" AllowSorting="true" AllowFiltering="true" Height="calc(100vh - 250px)">
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn AutoFit="true" Field="EmployeeNo" HeaderText="Employee No" TextAlign="TextAlign.Center" />
        <GridColumn AutoFit="true" Field="EmpCode" HeaderText="Emp Code" TextAlign="TextAlign.Center" />
        <GridColumn AutoFit="true" Field="FullName" HeaderText="Full Name" />
        <GridColumn AutoFit="true" Field="JobTitle" HeaderText="Job Title" />
        <GridColumn AutoFit="true" Field="LocationCode" HeaderText="Location Code" TextAlign="TextAlign.Center" />
        <GridColumn AutoFit="true" Field="LocationText" HeaderText="Location Text" />
        <GridColumn AutoFit="true" Field="EmployeeShiftCode" HeaderText="Shift Code" TextAlign="TextAlign.Center" />
        <GridColumn AutoFit="true" Field="EmployeeOfficialEmail" HeaderText="Official Email" />
        <GridColumn AutoFit="true" Field="DepartmentName" HeaderText="Department" />
        <GridColumn AutoFit="true" Field="PosText" HeaderText="Position" />
        <GridColumn AutoFit="true" Field="Status" HeaderText="Status" TextAlign="TextAlign.Center" />
    </GridColumns>
</SfGrid>

@code {
    private List<Employee2Dto> CurrentEmployees { get; set; } = new();
    protected override async Task OnInitializedAsync()
    {
        CurrentEmployees = await Service.GetAllEmployeeData();
        CurrentEmployees = await Service.ExcludeExists(CurrentEmployees);
    }

    private async Task SyncData()
    {
        var res = await Service.UpdateUsers(CurrentEmployees);

    }
}


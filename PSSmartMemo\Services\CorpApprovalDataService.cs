
using System.Net.Http.Json;
using Microsoft.Extensions.Configuration;


namespace PSSmartMemo.Services
{
    public class CorpApprovalDataService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;

        public CorpApprovalDataService(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _configuration = configuration;
        }

        public async Task<List<RINGIListItemDto>> GetRINGIListAsync(string empcode, int reqtype)
        {
            var baseUrl = _configuration.GetSection("apibaseurl").Value;
            // get last 5 digits of empcode
            empcode = empcode.Substring(empcode.Length - 5);
            
            try
            {
                var response = await _httpClient.GetAsync($"{baseUrl}RINGIList/{empcode},{reqtype}");
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<List<RINGIListItemDto>>() 
                       ?? new List<RINGIListItemDto>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching RINGI List: {ex.Message}");
                return new List<RINGIListItemDto>();
            }
        }
    }
}

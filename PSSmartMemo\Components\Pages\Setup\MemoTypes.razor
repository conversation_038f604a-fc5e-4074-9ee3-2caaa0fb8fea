@page "/setup/memo-types"
@inject MemoTypeDataService Service
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using Syncfusion.Blazor.Buttons
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Memo Types" Url=""></BreadcrumbItem>

    </BreadcrumbItems>
</SfBreadcrumb>
<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Memo Types</MudText>
    <MudButton Size="Size.Small" Variant="Variant.Filled" Color="Color.Primary"
               OnClick="OpenCreateForm"
               StartIcon="@MudBlazor.Icons.Material.Filled.Add">Create</MudButton>
</div>

<SfGrid DataSource="@memoTypes" AllowSorting="true" AllowFiltering="true" @ref="grid" Height="calc(100vh - 190px)">
    <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        
        <GridColumn Field="Title" HeaderText="Title" AutoFit="true"></GridColumn>
        <GridColumn Field="Code" HeaderText="Code" AutoFit="true"></GridColumn>
        <GridColumn Field="Status" HeaderText="Status" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Actions" AutoFit="true" TextAlign="TextAlign.Center">
            <Template>
                <SfButton CssClass="e-flat" IconCss="e-icons e-edit" OnClick="@(args => EditMemoType((context as MemoTypeDto)))"></SfButton>
                <SfButton CssClass="e-flat" IconCss="e-icons e-delete" OnClick="@(args => DeleteMemoType((context as MemoTypeDto)))"></SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog @ref="dialog" Width="400px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>@dialogTitle</Header>
        <Content>
            <EditForm Model="@memoType" OnValidSubmit="SaveMemoType">
                <DataAnnotationsValidator />
                <ValidationSummary />
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Title" @bind-Value="memoType.Title" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => memoType.Title)" />
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfTextBox Placeholder="Code" @bind-Value="memoType.Code" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                        <ValidationMessage For="@(() => memoType.Code)" />
                    </div>
                    <div class="col-md" style="display:flex; flex-direction: column; justify-content: space-between">
                        <label>Active</label>
                        <SfSwitch @bind-Checked="memoType.IsActive" OnLabel="Yes" OffLabel="No"></SfSwitch>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfButton CssClass="e-primary" Type="Submit">Save</SfButton>
                    </div>
                </div>
                
                
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private List<MemoTypeDto> memoTypes = new();
    private MemoTypeDto memoType = new();
    private string dialogTitle = "Add Memo Type";
    private SfGrid<MemoTypeDto> grid;
    private SfDialog dialog;

    protected override async Task OnInitializedAsync()
    {
        memoTypes = await Service.GetAll();
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
        }
    }

    private async Task OpenCreateForm()
    {
        memoType = new MemoTypeDto();
        memoType.IsActive = true;
        dialogTitle = "Add Memo Type";
        await dialog.ShowAsync();
    }

    private async Task EditMemoType(MemoTypeDto memoTypeDto)
    {
        memoType = memoTypeDto;
        dialogTitle = "Edit Memo Type";
        await dialog.ShowAsync();
    }

    private async Task SaveMemoType()
    {
        var result = await Service.Save(memoType, "user");
        if (result.Item2 == "OK")
        {
            memoTypes = await Service.GetAll();
            await dialog.HideAsync();
        }
        else
        {
            // Handle error
        }
    }

    private async Task DeleteMemoType(MemoTypeDto memoTypeDto)
    {
        var result = await Service.DeleteById(memoTypeDto.Id, userId);
        if (result == "OK")
        {
            memoTypes = await Service.GetAll();
        }
        else
        {
            // Handle error
        }
    }
}
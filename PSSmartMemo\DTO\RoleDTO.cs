﻿using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class RoleDTO
{
    public int Id { get; set; }

    public string Code { get; set; } = null!;

    [Required(ErrorMessage = "* Required")]
    [MaxLength(500, ErrorMessage = "* Max 500 characters are allowed")]
    public string Name { get; set; } = null!;

    [MaxLength(1000, ErrorMessage = "* Max 1000 characters are allowed")]
    public string Notes { get; set; } = null!;

    public bool IsActive { get; set; }
    public string Active => IsActive ? "Yes" : "No";
    public bool isSelect { get; set; }
}
﻿using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class UserDTO
{
    public int Id { get; set; }

    [Required] [StringLength(50)] public string UserId { get; set; }

    [Required(ErrorMessage = "* Name Required")]
    [MaxLength(500, ErrorMessage = "* Max 500 characters are allowed")]
    public string? Name { get; set; }

    public string? Code { get; set; }

    [MaxLength(15)] public string? EmployeeCode { get; set; }

    [MaxLength(1000)]
    [Required(ErrorMessage = "User Id is required")]
    // [DataType(DataType.EmailAddress, ErrorMessage = "* Invalid Email")]
    // [EmailAddress]
    public string? Email { get; set; }

    [MaxLength(1000)]
    [MinLength(6, ErrorMessage = "* At least 6 characters required")]
    [Required(ErrorMessage = "* Password Required")]
    public string? Password { get; set; }

    [MaxLength(1000)]
    [Required(ErrorMessage = "* Required")]
    [MinLength(6, ErrorMessage = "* At least 6 characters required")]
    [Compare("Password", ErrorMessage = "* Password Mismatched")]
    public string? ConfirmPassword { get; set; }

    [MaxLength(1000)] public string? Note { get; set; }

    [MaxLength(11, ErrorMessage = "* Max 11 characters are allowed")]
    public string? Mobile { get; set; }

    public bool IsAdmin { get; set; }
    public bool IsActive { get; set; }
    public string Active => IsActive ? "Yes" : "No";
    public string Admin => IsAdmin ? "Yes" : "No";

    public string? Title { get; set; }
    public string? EmployeeShiftCode { get; set; }
    public string? EmployeeShiftTitle { get; set; }

    public string? LocationText { get; set; }

    // department
    public string? Department { get; set; }
    public string? PosText { get; set; }
    public string? Status { get; set; }
}
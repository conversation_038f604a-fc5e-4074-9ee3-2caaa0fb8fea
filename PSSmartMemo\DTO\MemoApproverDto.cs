using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class MemoApproverDto
{
    public Guid Id { get; set; } = Guid.NewGuid();
    [StringLength(50)]
    [Required]
    public string? UserId { get; set; }
    [Required]
    [StringLength(300)]
    public string? User { get; set; }
    public string? Role { get; set; }
    [Required]
    [StringLength(300)]
    public string? Email { get; set; }
    [StringLength(300)]
    public string? Title { get; set; }
    public string? Status { get; set; }
    public int? sortOrder { get; set; }
    public string? allowType { get; set; }
    public string? userRoleType { get; set; }
    public int MemoApproverId { get; set; }
    public int? memoApproverRoleId { get; set; }
    public string? MemoApproverDesignation { get; set; }
    public string? Department { get; set; }
    public string? LastAction { get; set; }
    public DateTime? LastActionDate { get; set; }
    public string? ActionColor { get; set; }
    // deleted user info
    public string? DelegatedUser { get; set; }
    public string? DelegatedUserId { get; set; }
}

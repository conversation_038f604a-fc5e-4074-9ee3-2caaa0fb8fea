.approval-page-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    padding: 16px;
    height: calc(100vh - 120px);
    overflow: auto;
}

.approval-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e0e0e0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-left h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
    color: #333;
}

.project-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    color: white;
}

.memo-color {
    background: linear-gradient(135deg, #2b88d8, #1a5a9c);
}

.approval-content {
    flex: 1;
}

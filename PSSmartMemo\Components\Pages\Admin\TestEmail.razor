@inject CorporateService CorpService
@page "/setup/test-email"


<SfTextBox @bind-Value="email"></SfTextBox><br />
<SfTextBox @bind-Value="code"></SfTextBox><br />
<SfTextBox @bind-Value="message"></SfTextBox><br />
<SfButton @onclick="sendemail">Send Email</SfButton><br />

<label>@call</label>
@code
{
    private string email="jawaid akhter";
    private string code ="ABC\\1123\\112233";
    private string message = $"the quick brown fox \n\r jumps over lazy dog";
    private string call="";
    private async Task sendemail()
    {
        call = await CorpService.SendEmailAsync(email, code, message);
    }
}
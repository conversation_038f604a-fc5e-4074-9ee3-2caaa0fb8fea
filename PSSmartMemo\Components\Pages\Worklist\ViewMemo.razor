@page "/worklist/viewmemo/{Id}"
@using InputType = Syncfusion.Blazor.Inputs.InputType
@using MudBlazor
@using PSSmartMemo.Components.Shared

@inject WorklistDataService Service
@inject MemoDataService MemoService
@inject CorporateService adService
@inject IDialogService DialogService
@inject WatchListDataService WatchListService
@inject SignalRService SignalRService
@attribute [Authorize]
@implements IAsyncDisposable

<style>
    .page-block {
        margin-top: 40px;
        padding: 30px;
        padding-left: 30px;
        padding-right: 30px;
        background-color: white;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        border: 1px solid gray
    }

    .approvers-dialog-content {
        padding: 0;
    }
    
    .dialog-footer {
        padding: 10px;
        text-align: right;
    }

    .tmp-preview-container {
        padding: 15px;
        max-width: 100% !important;
        margin: 0 !important;
        margin-bottom: 30px !important;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border: 1px solid #aaa;
    }

    .tmp-preview-container:last-child {
        margin-bottom: 0 !important;
    }

    .memo-header-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .memo-header-info img {
        height: 26px !important;
        width: auto;
    }

    .memo-code {
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }

    .memo-title {
        font-size: 24px;
        margin: 0;
        color: #333;
    }

    .memo-initiated {
        font-size: 13px;
        color: #666;
        font-style: italic;
    }

    .tmp-preview-section-content {
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;
        margin-bottom: 15px !important;
    }

    .approver-table {
        border-collapse: collapse !important;
        width: 100% !important;
        margin-bottom: 15px !important;
    }

    .approver-table td,
    .approver-table th {
        border: 1px solid #000 !important;
        padding: 6px !important;
    }

    .no-print,
    .action-buttons,
    .navigation-controls {
        display: none !important;
    }

    img {
        max-width: 100% !important;
        height: auto !important;
    }

    .tmp-preview-section-block {
        margin-bottom: 20px;
    }

    .tmp-preview-section-header {
        margin-bottom: 10px;
    }

    .tmp-preview-section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .next-approver-box {
        display: flex;
        align-items: center;
        background-color: #f0f9f0;
        border-left: 4px solid #28a745;
        padding: 10px 15px;
        border-radius: 4px;
        margin-top: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .next-approver-box i {
        color: #28a745;
        margin-right: 12px;
        font-size: 18px;
    }

    .next-approver-label {
        font-size: 12px;
        color: #666;
        display: block;
    }

    .next-approver-name {
        font-weight: 600;
        color: #28a745;
        font-size: 14px;
    }

    .next-approver-role {
        font-size: 13px;
        color: #666;
        font-style: italic;
    }
</style>

<SfToast @ref="toastObj"></SfToast>
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Approval list" Url="/worklist"></BreadcrumbItem>
        <BreadcrumbItem Text="@memoObj.MemoTitle" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="tmp-preview-container">
    <div class="tmp-preview-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 40px; padding: 10px;">
            <div class="memo-header-info">
                <div><img src="images/logo.png" style="height: 20px" alt="logo" /></div>
                <span class="memo-code">@memoObj.MemoCode</span>
                <h1 class="memo-title">@(memoObj.MemoTitle?.ToUpper() ?? "")</h1>
                <div>
                    <div class="memo-initiated">Initiated By: @memoObj.InitiatedBy</div>
                    <div class="memo-initiated">Initiated Oy: @memoObj?.MemoCreatedDate?.ToString("d MMM, yyyy")</div>
                </div>
            </div>
            <table style="width:400px">
                <tr><td style="width:120px"><b>Department:</b>&nbsp;</td><td>@memoObj.Department</td></tr>
                <tr><td><b>Division:</b>&nbsp;</td><td>@memoObj.Division</td></tr>
            </table>
        </div>
    </div>

    <div class="tmp-preview-sections-wrapper">
        @foreach (var sec in memoObj.MemoSections.Where(s => !s.MemoSectionIgnored))
        {
            <div class="tmp-preview-section-block">
                <div class="tmp-preview-section-header">
                    <h3 class="tmp-preview-section-title">@sec.MemoSectionTitle</h3>
                </div>
                <div class="tmp-preview-section-content">
                    @((MarkupString)sec.MemoSectionContentHtml!)
                </div>
            </div>
        }
    </div>
</div>

<div class="tmp-preview-container">
    <div class="tmp-preview-section-block">
        <div class="tmp-preview-section-header">
            <h3 class="tmp-preview-section-title">Attachments</h3>
        </div>
        @if (attachments.Any())
        {
            <table class="approver-table">
                <thead>
                    <tr>
                        <th>File Name</th>
                        <th>Type</th>
                        <th>Size</th>
                        <th>Description</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var file in attachments)
                    {
                        <tr>
                            <td>@file.Name</td>
                            <td>@file.AttachmentType</td>
                            <td>@FormatFileSize(Convert.ToInt64(file.Size))</td>
                            <td>@file.Description</td>
                            <td>
                                <MudLink Href="@file.Path" Target="_blank">
                                    <MudIcon Icon="@Icons.Material.Filled.Download" />
                                    Download
                                </MudLink>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        }
        else
        {
            <MudText Typo="Typo.body2" Color="Color.Secondary">No attachments available</MudText>
        }
    </div>
</div>

<div class="tmp-preview-container">
    <div class="tmp-preview-section-block">
        <div class="tmp-preview-section-header">
            <h3 class="tmp-preview-section-title">Approval @approvalAction</h3>
        </div>
        <div class="tmp-preview-section-content">
            <div class="row mb-2">
                <div class="col-md">
                    <SfTextBox Enabled="!(canClose || canForward || IsObjectFormOpen || IsQueryFormOpen)"
                               Multiline="true" Placeholder="Comments" @bind-Value="currentState.DraftComments" FloatLabelType="FloatLabelType.Always"></SfTextBox>
                </div>
            </div>
            @if (IsQueryFormOpen)
            {
                <div class="row mb-2">
                    <div class="col-md">
                        <MudText Typo="Typo.h6">Query to User</MudText>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList DataSource="toApproverList" Placeholder="Approver"
                                        @bind-Value="toApproverId"
                                        TValue="int?"
                                        TItem="MemoApproverDto"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Value="@nameof(MemoApproverDto.MemoApproverId)"
                                                       Text="@nameof(MemoApproverDto.Title)"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <MudButton OnClick="@(() => PerformAction("query"))"
                                   Color="Color.Primary" Variant="Variant.Filled"
                                   Size="Size.Small">Send Query</MudButton>
                        <MudButton OnClick="@(() => {
                                                IsQueryFormOpen = false;
                                                IsObjectFormOpen = false;
                                                ResetUIState();
                                            })"
                                   Color="Color.Secondary" Variant="Variant.Filled"
                                   Size="Size.Small">Previous Screen</MudButton>
                    </div>
                </div>
            }
            else if (IsObjectFormOpen)
            {
                <div class="row mb-2">
                    <div class="col-md">
                        <MudText Typo="Typo.h6">Object to User</MudText>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <SfDropDownList DataSource="toApproverList" Placeholder="Approver"
                                        @bind-Value="toApproverId"
                                        TValue="int?"
                                        TItem="MemoApproverDto"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Value="@nameof(MemoApproverDto.MemoApproverId)"
                                                       Text="@nameof(MemoApproverDto.Title)"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md">
                        <MudButton OnClick="@(() => PerformAction("object"))"
                                   Color="Color.Primary" Variant="Variant.Filled"
                                   Size="Size.Small">Send Object</MudButton>
                        <MudButton OnClick="@(() => {
                                                IsObjectFormOpen = false;
                                                ResetUIState();
                                            })"
                                   Color="Color.Secondary" Variant="Variant.Filled"
                                   Size="Size.Small">Previous Screen</MudButton>
                    </div>
                </div>
            }
            else if (IsObjectFormOpen == false && IsQueryFormOpen == false)
            {
                <div class="row mb-2">
                    <div class="col-md">
                        <div style="display:flex;gap:5px;">
                            <SfTextBox Width="80px" @bind-Value="@password" Type="InputType.Password" FloatLabelType="FloatLabelType.Never" Placeholder="PIN"></SfTextBox>
                            @if (IsQueryMemo || IsObjectMemo)
                            {
                                <MudButton OnClick="@(() => PerformAction("reply"))" Color="Color.Primary" Variant="Variant.Filled" Size="Size.Small">Reply</MudButton>
                            }

                            <MudButton Disabled="@(canForward || canClose || IsQueryMemo || IsObjectMemo)" OnClick="@(() => PerformAction("approved"))" Color="Color.Primary" Variant="Variant.Filled" Size="Size.Small">Approve</MudButton>
                            <MudButton Disabled="@(canForward || canClose || IsQueryMemo)" OnClick="@(() => PerformAction("rejected"))" Color="Color.Warning" Variant="Variant.Filled" Size="Size.Small">Reject</MudButton>
                            <MudButton Disabled="@(canForward || canClose || IsQueryMemo)" OnClick="@(() => PerformAction("open-object"))" Color="Color.Error" Variant="Variant.Filled" Size="Size.Small">Object</MudButton>
                            <MudButton Disabled="@(canForward || canClose || IsObjectMemo || IsQueryMemo)" OnClick="@(() => PerformAction("open-query"))" Color="Color.Info" Variant="Variant.Filled" Size="Size.Small">Query</MudButton>
                            <MudButton Disabled="@(canForward || canClose || IsObjectMemo || IsQueryMemo || IsInitiator)" OnClick="@(() => PerformAction("skip"))" Color="Color.Default" Variant="Variant.Filled" Size="Size.Small">Skip</MudButton>
                            @if (canForward)
                            {
                                <MudButton OnClick="@(() => PerformAction("forward"))" Color="Color.Success" Variant="Variant.Filled" Size="Size.Small">Forward</MudButton>
                            }
                            @if (canClose)
                            {
                                <MudButton OnClick="@(() => PerformAction("close"))" Color="Color.Secondary" Variant="Variant.Filled" Size="Size.Small">Close</MudButton>
                            }
                            @if (canClose || canForward)
                            {
                                <MudButton OnClick="@(() => ResetUIState())" Color="Color.Tertiary" Variant="Variant.Filled" Size="Size.Small">Previous Screen</MudButton>
                            }
                        </div>
                    </div>
                    @if (nextApprover != null)
                    {
                        <div class="row mb-2">
                            <div class="col-md">
                                <div class="next-approver-box">
                                    <i class="fas fa-user-check"></i>
                                    <div>
                                        <span class="next-approver-label">Next approver:</span>
                                        <span class="next-approver-name">@nextApprover.User</span>
                                        <span class="next-approver-role">(@nextApprover.Role)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>

<div class="tmp-preview-container">
    <div class="tmp-preview-section-block">
        <div class="tmp-preview-section-header" style="display:flex;justify-content:space-between;align-items:center">
            <h3 class="tmp-preview-section-title">Approvers</h3>
            <MudButton Variant="Variant.Outlined"
                       Color="Color.Primary"
                       Size="Size.Small"
                       OnClick="OpenApproversDialog">
                View Approval Logs
            </MudButton>
        </div>
        <div class="tmp-preview-section-content">
            <ApproversPopup MemoId="@memoObj.MemoId" />
        </div>
    </div>
</div>

<SfDialog @ref="approversDialog" Width="800px" IsModal="true" ShowCloseIcon="true" Visible="false">
    <DialogTemplates>
        <Header>Memo Approval Logs</Header>
        <Content>
            <div class="approvers-dialog-content">
                
                <ApprovalTimeline ApprovalLogs="@approvalLogs" />
            </div>
        </Content>
        <FooterTemplate>
            <div class="dialog-footer">
                <SfButton CssClass="e-primary" OnClick="@(() => approversDialog.HideAsync())">Close</SfButton>
            </div>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>

@code {
    [Parameter] public string? Id { get; set; }
    private int _logId;
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }
    private string userId = "";
    private string password = "";
    private MemoDto memoObj = new();
    private List<MemoApprovalLogDto> approvalLogs = new();
    private MemoApprovalLog currentState = new MemoApprovalLog();
    private List<MemoAttachmentDto> attachments = new();
    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
            }
        }

        if (string.IsNullOrEmpty(userId))
        {
            NavMgr.NavigateTo("/");
        }

        try
        {
            if (Id != null)
            {
                _logId = Convert.ToInt32(Id);
            }
        }
        catch
        {
            _logId = 0;
        }

        if (_logId == 0)
        {
            NavMgr.NavigateTo("/worklist");
        }

        // is this item blong to user
        // if not then(direct to worklist
        var isMyMemoItem = await Service.IsMyMemoItem(_logId, userId);
        if (!isMyMemoItem)
        {
            NavMgr.NavigateTo("/worklist");
        }

        memoObj = await Service.GetMemoByLogId(_logId);
        approvalLogs = await Service.GetMemoApprovalLogs(memoObj.MemoId);
        currentState = await Service.GetCurrentMemoState(_logId);
        canForward = await Service.CanForward(_logId);
        canClose = await Service.CanClose(_logId);

        IsQueryMemo = await Service.IsQueryMemo(_logId);
        IsObjectMemo = await Service.IsObjectMemo(_logId);

        // Get department and division values
        var (division, department) = await MemoService.GetUserDivisionAndDeptByMemoId(memoObj.MemoId);
        memoObj.Division = division;
        memoObj.Department = department;

        IsObjectFormOpen = false;
        IsQueryFormOpen = false;
        canForward = false;
        canClose = false;
        //IsObjectFormOpen = await Service.IsObjectFormOpen(_logId);
        //IsQueryFormOpen = await Service.IsQueryFormOpen(_logId);
        ////currentComment = currentState.DraftComments;
        //if (IsObjectFormOpen || IsQueryFormOpen)
        //{
        //    var cc = await Service.GetLogById(_logId);
        //    toApproverList = await Service.GetUpperApprovers(_logId);
        //    toApproverId = cc.DraftToUserId;
        //}

        attachments = await Service.GetMemoAttachments(_logId);
    }

    private bool canForward = false;
    public bool canClose = false;
    //private string currentComment = "";
    private SfToast? toastObj;
    private int? toApproverId = -1;
    private bool IsQueryMemo = false;
    private bool IsObjectMemo = false;
    private bool IsObjectFormOpen = false;
    private bool IsQueryFormOpen = false;
    private List<MemoApproverDto> toApproverList = new();
    private string _userAction = "";
    private MemoApproverDto? nextApprover = null;
    private SfDialog approversDialog;

    private async Task PerformAction3(string action)
    {
        canForward = false;
        canClose = false;

        if (_userAction == "approved")
        {
            bool nextApproverExist = await Service.NextApproverExists(_logId);
            if (nextApproverExist)
            {
                canForward = true;
                canClose = true;
            }
            else
            {
                canForward = false;
                canClose = true;
            }
            //await Service.PerformAction(_userAction, _logId, -1, currentState.DraftComments);
            //await Service.PerformForward(_logId);
        }
        else if (_userAction == "rejected")
        {

        }
    }

    private async Task<MemoApproverDto?> GetNextApprover(int logId)
    {
        // Get the current log entry
        var log = await Service.GetLogById(logId);

        // Get all approvers for this memo
        var approvers = await WatchListService.GetMemoApprovers(log.MemoId);

        // Get current approver's sort order
        var currentApprover = approvers.FirstOrDefault(a => a.MemoApproverId == log.ToApproverId);
        if (currentApprover == null) return null;

        // Get next approver based on sort order
        var nextApprover = approvers
            .Where(a => a.sortOrder > currentApprover.sortOrder)
            .OrderBy(a => a.sortOrder)
            .FirstOrDefault();

        return nextApprover;
    }

    private async Task PerformForward()
    {
        string msg = await Service.PerformForward(_logId, currentState, userId);
        if (msg == "OK")
        {
            NavMgr.NavigateTo("/worklist");
        }
    }

    private async Task PerformAction(string action)
    {
        if (string.IsNullOrEmpty(password))
        {
            await ShowToast("Error", "PIN is required");
            return;
        }

        bool isValidPassword = await adService.ValidatePinAsync(userId, password);
        if (!isValidPassword)
        {
            await ShowToast("Error", "Invalid User Password");
            return;
        }

        // Reset next approver when starting a new action
        nextApprover = null;

        if (action == "skip")
        {
            string msg = await Service.PerformSkip(_logId, currentState.DraftComments, userId);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                await ShowToast("Error", msg);
            }
            return;
        }

        // Reset UI state for new actions
        IsObjectFormOpen = false;
        IsQueryFormOpen = false;
        _userAction = "";

        // Set default comments for approve action if empty
        if (action == "approved" && string.IsNullOrEmpty(currentState.DraftComments))
        {
            currentState.DraftComments = "Approved";
        }
        // Validate comments for other actions
        else if (action != "approved" && string.IsNullOrEmpty(currentState.DraftComments))
        {
            await ShowToast("Error", "Please enter comments");
            return;
        }

        if (action == "open-object")
        {
            isValidPassword = await adService.ValidatePinAsync(userId, password);
            if (isValidPassword)
            {
                toApproverList = await Service.GetUpperApprovers(_logId);
                IsObjectFormOpen = true;
                IsQueryFormOpen = false;
                //password = "";
                canForward = false;
                canClose = false;
                approvalAction = " (Object)";
            }
            else
            {
                await ShowToast("Error", "Invalid User Password");
            }
        }
        else if (action == "open-query")
        {
            isValidPassword = await adService.ValidatePinAsync(userId, password);
            if (isValidPassword)
            {
                toApproverList = await Service.GetUpperApprovers(_logId);
                IsQueryFormOpen = true;
                IsObjectFormOpen = false;
                //password = "";

                canForward = false;
                canClose = false;
                approvalAction = " (Query)";
            }
            else
            {
                var tm = new ToastModel()
                {
                    Title = "Error",
                    Content = "Invalid User Password",
                    ShowProgressBar = true,
                    Timeout = 5000
                };
                await toastObj.ShowAsync(tm);
            }
        }
        else if (action == "approved")
        {
            // if next state available then enable forward button
            isValidPassword = await adService.ValidatePinAsync(userId, password);
            if (isValidPassword)
            {
                var nextApproverExists = await Service.NextApproverExists(_logId);
                canForward = nextApproverExists;
                canClose = !nextApproverExists;
                _userAction = action;
                approvalAction = " (Approve)";

                // Get next approver information if it exists
                if (nextApproverExists)
                {
                    nextApprover = await GetNextApprover(_logId);
                }
                else
                {
                    nextApprover = null;
                }
            }
            else
            {
                var tm = new ToastModel()
                {
                    Title = "Error",
                    Content = "Invalid User Password",
                    ShowProgressBar = true,
                    Timeout = 5000
                };
                if (toastObj != null)
                {
                    await toastObj.ShowAsync(tm);
                }
            }
        }
        else if (action == "rejected")
        {
            isValidPassword = await adService.ValidatePinAsync(userId, password);
            if (isValidPassword)
            {
                canForward = false;
                canClose = true;
                _userAction = action;
                approvalAction = " (Reject)";
            }
            else
            {
                var tm = new ToastModel()
                {
                    Title = "Error",
                    Content = "Invalid User Password",
                    ShowProgressBar = true,
                    Timeout = 5000
                };
                if (toastObj != null)
                {
                    await toastObj.ShowAsync(tm);
                }
            }
        }
        else if (action == "object")
        {
            string msg = await Service.PerformObject(_logId, toApproverId, currentState.DraftComments, userId);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                var tm = new ToastModel()
                {
                    Title = "Error",
                    Content = msg,
                    ShowProgressBar = true,
                    Timeout = 5000
                };
                if (toastObj != null)
                {
                    await toastObj.ShowAsync(tm);
                }
            }
        }
        else if (action == "query")
        {
            string msg = await Service.PerformQuery(_logId, toApproverId, currentState.DraftComments, userId);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                var tm = new ToastModel()
                {
                    Title = "Error",
                    Content = msg,
                    ShowProgressBar = true,
                    Timeout = 5000
                };
                if (toastObj != null)
                {
                    await toastObj.ShowAsync(tm);
                }
            }
        }
        else if (action == "reply")
        {
            var msg = await Service.PerformReply(_logId, currentState.DraftComments, userId);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
                var mm = new ToastModel()
                {
                    Content = msg,
                    Title = "Error",
                    Timeout = 5000,
                    ShowProgressBar = true,
                    ShowCloseButton = true
                };
                if (toastObj != null)
                {
                    await toastObj.ShowAsync(mm);
                }
            }

        }
        else if (action == "close")
        {
            var msg = await Service.PerformClose(_logId, currentState.DraftComments, approvalAction, userId);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                var mm = new ToastModel()
                {
                    Content = msg,
                    Title = "Error",
                    Timeout = 5000,
                    ShowProgressBar = true,
                    ShowCloseButton = true
                };
                if (toastObj != null)
                {
                    await toastObj.ShowAsync(mm);
                }
            }
        }
        else if (action == "forward")
        {
            var msg = await Service.PerformForward(_logId, currentState, userId);
            if (msg == "OK")
            {
                NavMgr.NavigateTo("/worklist");
            }
            else
            {
                var mm = new ToastModel()
                {
                    Content = msg,
                    Title = "Error",
                    Timeout = 5000,
                    ShowProgressBar = true,
                    ShowCloseButton = true
                };
                if (toastObj != null)
                {
                    await toastObj.ShowAsync(mm);
                }
            }
        }
    }


    private string approvalAction = "";

    private async Task ShowToast(string title, string content, bool isError = true)
    {
        if (toastObj == null) return;

        var model = new ToastModel
        {
            Title = title,
            Content = content,
            ShowProgressBar = true,
            Timeout = 5000,
            ShowCloseButton = isError
        };

        await toastObj.ShowAsync(model);
    }

    // Helper method to reset the UI state
    private void ResetUIState()
    {
        canClose = false;
        canForward = false;
        approvalAction = "";
        nextApprover = null;
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private bool IsInitiator
    {
        get
        {
            var approver = memoObj?.MemoApprovers?.FirstOrDefault(a => a.MemoApproverUserId == userId);
            return approver?.MemoApproverRole?.MemoApproverRoleTitle == "Initiator";
        }
    }

    //private async Task ShowToast(string title, string content, bool isError = true)
    //{
    //    if (toastObj == null) return;

    //    var model = new ToastModel
    //    {
    //        Title = title,
    //        Content = content,
    //        ShowProgressBar = true,
    //        Timeout = 5000,
    //        ShowCloseButton = isError
    //    };

    //    await toastObj.ShowAsync(model);
    //}

    private async Task OpenApproversDialog()
    {
        await approversDialog.ShowAsync();
    }

    private string GetActionColor(string action)
    {
        return action.ToLower() switch
        {
            "approved" => "approved",
            "rejected" => "rejected",
            "object" => "object",
            "query" => "query",
            "forward" => "forward",
            "skip" => "skip",
            "reply" => "reply",
            _ => "forward"
        };
    }

    private string GetActionIcon(string action)
    {
        return action.ToLower() switch
        {
            "approved" => Icons.Material.Filled.Check,
            "rejected" => Icons.Material.Filled.Close,
            "object" => Icons.Material.Filled.Warning,
            "query" => Icons.Material.Filled.Help,
            "forward" => Icons.Material.Filled.Forward,
            "skip" => Icons.Material.Filled.SkipNext,
            "reply" => Icons.Material.Filled.Reply,
            _ => Icons.Material.Filled.Info
        };
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await SignalRService.InitializeAsync();
        SignalRService.OnApprovalUpdate += HandleApprovalUpdate;
    }

    private async void HandleApprovalUpdate(int updatedMemoId, int updatedLogId, string action, string fromUser)
    {
        if (updatedMemoId == memoObj.MemoId && fromUser != userId)
        {
            await InvokeAsync(() =>
            {
                NavMgr.NavigateTo("/worklist", true);
            });
        }
    }

    public async ValueTask DisposeAsync()
    {
        SignalRService.OnApprovalUpdate -= HandleApprovalUpdate;
        await ValueTask.CompletedTask;
    }
}

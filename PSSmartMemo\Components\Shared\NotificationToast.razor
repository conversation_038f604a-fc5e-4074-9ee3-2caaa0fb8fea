@inject IJSRuntime js
@inject SignalRService SignalRService
@implements IAsyncDisposable

<SfToast @ref="toastObj" ID="toast_default">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

@code {
    private SfToast toastObj;
    
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await js.InvokeVoidAsync("storeToastReference", toastObj);
            await SignalRService.InitializeAsync();
            SignalRService.OnApproverNotification += HandleApproverNotification;
        }
    }
    
    private async void HandleApproverNotification(string notifiedUserId, string memoTitle, string memoCode)
    {
        // Get current user ID from authentication state
        var authState = await AuthenticationState;
        var currentUserId = authState.User.Identity?.Name;
        
        // Show notification if it's for current user
        if (notifiedUserId == currentUserId)
        {
            await InvokeAsync(async () =>
            {
                await toastObj.ShowAsync(new ToastModel
                {
                    Title = "New Memo Requires Approval",
                    Content = $"Memo {memoCode}: {memoTitle}",
                    ShowProgressBar = true,
                    Timeout = 10000,
                    CssClass = "e-toast-info",
                    ShowCloseButton = true
                });
            });
        }
    }
    
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationState { get; set; }
    
    public async ValueTask DisposeAsync()
    {
        SignalRService.OnApproverNotification -= HandleApproverNotification;
        await ValueTask.CompletedTask;
    }
}
﻿@inject TemplateDataService Service
@inject NavigationManager NavMgr
@page "/setup/templates/new"
@page "/setup/templates/edit/{TemplateId:int}"
@using ButtonType = MudBlazor.ButtonType
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@rendermode InteractiveServer
<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Templates" Url="/setup/templates"></BreadcrumbItem>
        <BreadcrumbItem Text="Detail"></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>
<SfToast @ref="toastObj"></SfToast>
<div class="row mb-2">
    <div class="col-md">
        <MudText Typo="Typo.h5">Template Detail</MudText>
    </div>
</div>



<EditForm OnValidSubmit="SaveTemplate" Model="tempObj">
    <div class="row">
        <div class="col-md">
            <DataAnnotationsValidator></DataAnnotationsValidator>
            <ValidationSummary></ValidationSummary>
        </div>
    </div>
    <div class="row mt-2">
        <div class="col-md">
            <SfTextBox Placeholder="Template Title"
                       FloatLabelType="FloatLabelType.Always"
                       @bind-Value="tempObj.TemplateTitle"></SfTextBox>
        </div>
    </div>
    <div class="row mt-2">
        <div class="col-md-8">
            <SfDropDownList Placeholder="Memo Type"
                            FloatLabelType="FloatLabelType.Always"
                            DataSource="memoTemplateTypesList"
                            AllowFiltering="true"
                            PopupWidth="400px"
                            FilterType="FilterType.Contains"
                            @bind-Value="tempObj!.MemoTypeId">
                <DropDownListFieldSettings Value="@nameof(MemoTypeDto.Id)"
                                           Text="@nameof(MemoTypeDto.Title)"></DropDownListFieldSettings>
            </SfDropDownList>
        </div>
        <div class="col-md-4">
            <SfTextBox Placeholder="Code" FloatLabelType="FloatLabelType.Always"
                       @bind-Value="tempObj.MemoTemplateCode" />
        </div>
        @* <div class="col-md-2"> *@
        @*     <SfDropDownList Placeholder="Form Type" *@
        @*                     FloatLabelType="FloatLabelType.Always" *@
        @*                     DataSource="formTypesList" *@
        @*                     AllowFiltering="true" *@
        @*                     FilterType="FilterType.Contains" *@
        @*                     @bind-Value="tempObj.FormTypeId"> *@
        @*         <DropDownListFieldSettings Value="@nameof(FormTypeDto.Id)" *@
        @*                                    Text="@nameof(FormTypeDto.Title)"></DropDownListFieldSettings> *@
        @*     </SfDropDownList> *@
        @* </div> *@


    </div>




    @foreach (var sec in tempObj.Sections)
    {
        <div class="sec-sep"></div>
        <div class="row mt-3">
            <div class="col-md">
                <SfTextBox Placeholder="Section Title"
                           FloatLabelType="FloatLabelType.Always"
                           @bind-Value="sec.SectionTitle"></SfTextBox>
                <ValidationMessage For="@(() => sec.SectionTitle)"></ValidationMessage>
            </div>
            <div class="col-md-2" style="display: flex; align-items: flex-end;">
                <SfButton CssClass="e-flat" IconCss="e-icons e-arrow-up"
                          type="button"
                          OnClick="() => MoveSectionUp(sec)">
                </SfButton>
                <SfButton type="button" CssClass="e-flat" IconCss="e-icons e-arrow-down"
                          OnClick="() => MoveSectionDown(sec)">
                </SfButton>
                <SfButton type="button" CssClass="e-flat" IconCss="e-icons e-trash" OnClick="() => DeleteSection(sec)">
                </SfButton>
            </div>
            <div class="col-md-2" style="display: flex; align-items: flex-end;">
                <div>
                    <label>Required</label><br />
                    <SfSwitch OnLabel="Yes" OffLabel="No" @bind-Checked="sec.IsRequired"></SfSwitch>
                </div>
            </div>
            <div class="col-md-2" style="display: flex; align-items: flex-end;">
                <div>
                    <label>Watermark</label><br />
                    <SfSwitch OnLabel="Yes" OffLabel="No" @bind-Checked="sec.IsWaterMark"></SfSwitch>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col">
                <SfRichTextEditor Placeholder="Section Content"
                                   Width="100%"
                                  @bind-Value="sec.ContentHtml">
                    <RichTextEditorToolbarSettings Items="@Tools" Type="ToolbarType.Expand" />
                </SfRichTextEditor>
                <ValidationMessage For="@(() => sec.ContentHtml)"></ValidationMessage>
            </div>
        </div>
    }
    <div class="row mt-2">
        <div class="col-md">
            <MudButton OnClick="AddTemplateSection" Size="Size.Small" Variant="Variant.Filled" Color="Color.Info"
                       ButtonType="ButtonType.Button"
                       StartIcon="@Icons.Material.Filled.AddComment">
                Add Section
            </MudButton>
        </div>
    </div>
    <div style="border: 1px solid #ccc; background-color: #eee;padding: 10px; border-radius:10px;" class="mt-3">
        <div class="row">
            <div class="col-md">
                <MudText Typo="Typo.h5">Attachment Detail</MudText>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-1"></div>
            <div class="col-md-2" style="display: flex;align-items: flex-end">
                <div>
                    <label>Attachment Allowed</label><br />
                    <SfSwitch OnLabel="Yes"
                              OffLabel="No"
                              @bind-Checked="tempObj.MemoTemplateAttachmentAllowed"></SfSwitch>
                </div>
            </div>


            <div class="col-md-3">
                <SfNumericTextBox Placeholder="Max Attachments"
                                  FloatLabelType="FloatLabelType.Always"
                                  @bind-Value="tempObj.MemoTemplateAttachmentFileCountAllowed"
                                  ShowClearButton="true"
                                  ShowSpinButton="false"
                                  Readonly="@(tempObj.MemoTemplateAttachmentAllowed == false)"
                                  Decimals="0" Format="####"
                                  Min="1" Max="100"></SfNumericTextBox>
            </div>
            <div class="col-md-3">
                <SfNumericTextBox Placeholder="Allowed Attachment Size MB (max)"
                                  FloatLabelType="FloatLabelType.Always"
                                  @bind-Value="tempObj.MemoTemplateAttachmentPerFileSizeMbAllowed"
                                  ShowClearButton="true"
                                  ShowSpinButton="false"
                                  Readonly="@(tempObj.MemoTemplateAttachmentAllowed == false)"
                                  Decimals="0" Format="###,###"
                                  Min="1" Max="999999"></SfNumericTextBox>
            </div>


        </div>
    </div>
    <div class="row">
        <div class="col-md-1">

        </div>
        <div class="col-md">
            <div class="col-md">

                <label class="mt-2">Is Active</label><br />
                <SfSwitch OnLabel="Yes" OffLabel="No" @bind-Checked="tempObj.IsActive"></SfSwitch>
            </div>
        </div>
    </div>
    @*<div class="row mt-3">
            <div class="col-md-4">
                <SfNumericTextBox Placeholder="Max Approver"
                                  FloatLabelType="FloatLabelType.Always"
                                  @bind-Value="tempObj.MemoTemplateApproverCountAllowed"
                                  ShowClearButton="true"
                                  ShowSpinButton="false"
                                  Decimals="0" Format="####"
                                  Min="1" Max="100"></SfNumericTextBox>
            </div>
            <div class="col-md">

                <label class="mt-2">Is Active</label><br/>
                <SfSwitch OnLabel="Yes" OffLabel="No" @bind-Checked="tempObj.IsActive"></SfSwitch>
            </div>
        </div>*@
<div style="border: 1px solid #ccc; background-color: #eee;padding: 10px; border-radius:10px;" class="mt-3">
    <div class="row">
        <div class="col-md">
            <MudText Typo="Typo.h5">Approver Detail</MudText>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-md">
            <SfDropDownList DataSource="approverRolesList" AllowFiltering="true" FilterType="FilterType.Contains"
                            @bind-Value="ApproverRoleId"
                            Placeholder="Approver Role"
                            TValue="int?"
                            TItem="ApproverRoleDto"
                            FloatLabelType="FloatLabelType.Always">
                <DropDownListFieldSettings Value="Id" Text="Title"></DropDownListFieldSettings>
            </SfDropDownList>
        </div>
        <div class="col-md-3">
            <SfDropDownList DataSource="allowTypeList" AllowFiltering="true" FilterType="FilterType.Contains"
                            @bind-Value="AllowType"
                            Placeholder="Allow Type"
                            TValue="string"
                            TItem="string"
                            FloatLabelType="FloatLabelType.Always">
            </SfDropDownList>
        </div>
        <div class="col-md-2" style="display:flex;align-items:flex-end">
            <SfButton CssClass="e-flat" IconCss="e-icons e-check-tick"
                      OnClick="AddApprover"
                      type="button" style="width:100%">Add</SfButton>
        </div>

    </div>
    <div class="row mt-3">
        <div class="col-md">
            <table class="approver-table">
                <thead>
                    <tr>
                        <th>
                            Approver Type
                        </th>
                        <th style="width:180px">
                            AllowType
                        </th>
                        <th style="width:149px">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var ap in tempObj.Approvers)
                    {
                        <tr>
                            <td>@ap.ApproverRoleTitle</td>
                            <td>@ap.AllowType   </td>
                            <td>
                                <SfButton CssClass="e-flat" IconCss="e-icons e-arrow-up"
                                          type="button"
                                          OnClick="() => MoveApproverUp(ap)">


                                </SfButton>
                                <SfButton type="button" CssClass="e-flat" IconCss="e-icons e-arrow-down"
                                          OnClick="() => MoveApproverDown(ap)">

                                </SfButton>
                                <SfButton type="button" CssClass="e-flat" IconCss="e-icons e-trash" OnClick="() => DeleteApprover(ap)">

                                </SfButton>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>

</div>
        @if (tempObj.Sections.Any())
        {
            <div class="row mt-3">
                <div class="col">
                    <MudButton Color="Color.Success"
                               ButtonType="ButtonType.Submit"
                               Size="Size.Small"
                               Variant="Variant.Filled"
                               StartIcon="@Icons.Material.Filled.Save">
                        Save
                    </MudButton>
                </div>
            </div>
        }
</EditForm>

@code {
    [Parameter] public int? TemplateId { get; set; }
    private SfDialog? dlgFormRole;
    private bool isChecked = false;
    private SfToast? toastObj;
    private TemplateDto? tempObj = new();
    private List<MemoTypeDto> memoTemplateTypesList = new();
    private List<FormTypeDto> formTypesList = new();

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        memoTemplateTypesList = await Service.GetMemoTypes();
        approverRolesList = await Service.GetAllMemoApproverRoles();
        formTypesList = await Service.GetFormTypes();
        if (TemplateId.HasValue)
        {
            tempObj = await Service.GetById(TemplateId.Value);
            if (tempObj == null)
                NavMgr.NavigateTo("/setup/templates");
        }
        else
        {
            tempObj = new TemplateDto();
        }
    }

    private void AddTemplateSection(MouseEventArgs obj)
    {
        tempObj!.Sections.Add(new TemplateSectionDto { IsRequired = true, TemplateSectionId = Guid.NewGuid() });
    }

    private void MoveSectionUp(TemplateSectionDto section)
    {
        var index = tempObj!.Sections.IndexOf(section);
        if (index > 0)
        {
            tempObj.Sections.RemoveAt(index);
            tempObj.Sections.Insert(index - 1, section);
        }
    }

    private void MoveSectionDown(TemplateSectionDto section)
    {
        var index = tempObj!.Sections.IndexOf(section);
        if (index < tempObj.Sections.Count - 1)
        {
            tempObj.Sections.RemoveAt(index);
            tempObj.Sections.Insert(index + 1, section);
        }
    }

    private void DeleteSection(TemplateSectionDto section)
    {
        tempObj!.Sections.Remove(section);
    }

    private async Task SaveTemplate(EditContext arg)
    {
        var res = await Service.Save(tempObj!, "jawaid");
        if (res.Item2 == "OK")
        {
            await toastObj!.ShowAsync(new ToastModel
            {
                Content = "Template saved successfully",
                CssClass = "e-toast-success",
                Icon = "e-success toast-icons",
                ShowCloseButton = true,
                ShowProgressBar = true
            });
        }
        else
        {
            await toastObj!.ShowAsync(new ToastModel
            {
                Content = "Error saving template: " + res.Item2,
                CssClass = "e-toast-danger",
                Icon = "e-error toast-icons",
                ShowCloseButton = true,
                ShowProgressBar = true
            });
        }

        if (TemplateId.HasValue && res.Item2 == "OK")
        {
            tempObj!.TemplateId = res.Item1.TemplateId;
            TemplateId = res.Item1.TemplateId;
        }
        //NavMgr.NavigateTo("/setup/templates/edit/"+res.Item1);
    }


    private readonly List<string?> allowTypeList = ["Optional", "Must Required", "Must Not Required"];

    //private readonly List<string?> approverTypeList =
    //[
    //    "Generic", "Head of Department", "Functional Head", "Divisional Head", "Managing Director",
    //    "CFO", "COO"
    //];

    private List<ApproverRoleDto> approverRolesList = new();


    private string ApproverTitle = "";
    private string? AllowType = "";
    private readonly List<ToolbarItemModel> Tools =
    [
        new() { Command = ToolbarCommand.Bold },
        new() { Command = ToolbarCommand.Italic },
        new() { Command = ToolbarCommand.Underline },
        new() { Command = ToolbarCommand.StrikeThrough },
        new() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.FontColor },
        new() { Command = ToolbarCommand.BackgroundColor },
        new() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.Formats },
        new() { Command = ToolbarCommand.Alignments },
        new() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.OrderedList },
        new() { Command = ToolbarCommand.UnorderedList },
        new() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.Outdent },
        new() { Command = ToolbarCommand.Indent },
        new() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.CreateLink },
        new() { Command = ToolbarCommand.Image },
        new() { Command = ToolbarCommand.CreateTable },
        new() { Command = ToolbarCommand.Separator },
        new() { Command = ToolbarCommand.Undo },
        new() { Command = ToolbarCommand.Redo }
    ];

    public List<MemoTemplateApproverDto> templateApprovers { get; set; }
    public int? ApproverRoleId { get; set; }

    private void MoveApproverUp(MemoTemplateApproverDto apprpver)
    {
        var index = tempObj!.Approvers.IndexOf(apprpver);
        if (index > 0)
        {
            tempObj.Approvers.RemoveAt(index);
            tempObj.Approvers.Insert(index - 1, apprpver);
        }
    }

    private void MoveApproverDown(MemoTemplateApproverDto ap)
    {
        var index = tempObj!.Approvers.IndexOf(ap);
        if (index < tempObj.Approvers.Count - 1)
        {
            tempObj.Approvers.RemoveAt(index);
            tempObj.Approvers.Insert(index + 1, ap);
        }
    }

    private void DeleteApprover(MemoTemplateApproverDto ap)
    {
        tempObj!.Approvers.Remove(ap);
    }

    private void AddApprover()
    {
        if (tempObj != null)
        {
            if (ApproverRoleId!=null &&
                !string.IsNullOrEmpty(AllowType))
            {

                tempObj.Approvers.Add(new MemoTemplateApproverDto()
                {
                    ApproverRoleTitle = approverRolesList.First(c=>c.Id==ApproverRoleId).Title,
                    AllowType = AllowType, MemoApproverRoleId=ApproverRoleId
                });
                ApproverTitle = "";
                ApproverRoleId = null;
                AllowType = "";
            }

        }
    }

}

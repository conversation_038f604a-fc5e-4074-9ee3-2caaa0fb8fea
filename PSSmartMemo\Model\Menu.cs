﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class Menu
{
    public int Id { get; set; }

    public int ModuleId { get; set; }

    public string Code { get; set; }

    public string Name { get; set; }

    public string Url { get; set; }

    public string Device { get; set; }

    public bool IsActive { get; set; }

    public int? MenuParentId { get; set; }

    public DateTime? CreatedDate { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int? ModifiedBy { get; set; }

    public int? SortOrder { get; set; }

    public string Urltarget { get; set; }

    public string Icon { get; set; }

    public virtual ICollection<RoleMenu> RoleMenus { get; set; } = new List<RoleMenu>();
}
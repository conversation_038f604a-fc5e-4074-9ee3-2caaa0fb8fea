﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace PSSmartMemo.Model;

public partial class MemoTemplateApprover
{
    public Guid MemoTemplateApproverId { get; set; }

    public string MemoTemplateApproverCode { get; set; }

    public string MemoTemplateApproverTitle { get; set; }

    public string MemoTemplateApproverUserId { get; set; }

    public string MemoTemplateApproverUserEmail { get; set; }

    public string MemoTemplateApproverUserName { get; set; }

    public string MemoTemplateApproverUserDetail { get; set; }

    public int? MemoTemplateApproverSortOrder { get; set; }

    public string MemoTemplateApproverAllowType { get; set; }

    public bool MemoTemplateApproverIsActive { get; set; }

    public bool MemoTemplateApproverIsDel { get; set; }

    public DateTime? MemoTemplateApproverCreatedDate { get; set; }

    public string MemoTemplateApproverCreatedBy { get; set; }

    public DateTime? MemoTemplateApproverModifiedDate { get; set; }

    public string MemoTemplateApproverModifiedBy { get; set; }

    public int? MemoTemplateId { get; set; }

    public int? MemoApproverRoleId { get; set; }

    public virtual MemoApproverRole MemoApproverRole { get; set; }

    public virtual MemoTemplate MemoTemplate { get; set; }
}
@layout PreviewLayout
@page "/setup/templates/view/{TemplateId:int}"
@using PSSmartMemo.Components.Layout
@inject IJSRuntime JS
@inject TemplateDataService Service
@rendermode InteractiveServer
<style>
  /* Base styles */
</style>
<div class="tmp-preview-container">
  <div class="tmp-preview-header">
    <div style="display: flex; justify-content: space-between; align-items: center; gap: 40px; padding: 10px;">
      <img src="images/logo.png" style="height: 30px" alt="logo"/>
      <div style="text-align: right">
        <h1 class="tmp-preview-main-title">@template!.TemplateTitle (Template)</h1>
        <p>@template.MemoType</p>
      </div>
      
    </div>
    @*<div class="tmp-preview-meta-info">
      <span class="tmp-preview-meta-item"><b>Code:</b> @template.MemoTemplateCode</span>
      <span class="tmp-preview-meta-item"><b>Prefix:</b> @template.MemoTemplatePrefixCode</span>
      <span class="tmp-preview-meta-item"><b>Status:</b> @(template.IsActive ? "Active" : "Inactive")</span>
    </div>*@
  </div>

  

  <div class="tmp-preview-sections-wrapper">
    
    @foreach (var sec in template.Sections)
    {
      <div class="tmp-preview-section-block">
        <div class="tmp-preview-section-header">
          <h3 class="tmp-preview-section-title">@sec.SectionTitle</h3>
          @if (sec.IsRequired)
          {
            <span class="tmp-preview-required-tag">Required</span>
          }
        </div>
        <div class="tmp-preview-section-content">
          @((MarkupString)sec.ContentHtml!)
        </div>
      </div>
    }
  </div>

  <div class="tmp-preview-footer">
    <p>Generated on @DateTime.Now.ToShortDateString()</p>
  </div>
</div>
@code {
  [Parameter] public int TemplateId { get; set; }
  private TemplateDto? template;

  protected override async Task OnParametersSetAsync()
  {
    template = await Service.GetById(TemplateId);
  }

  private async Task Print()
  {
    await JS.InvokeVoidAsync("print");
  }

}
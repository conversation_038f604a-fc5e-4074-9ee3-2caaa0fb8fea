﻿using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class OfficeLocationDto
{
    public int Id { get; set; }
    public string? Location { get; set; }
    public bool IsActive { get; set; }
    public string Status => IsActive ? "Active" : "InActive";
}

public class MeetingRoomDto
{
    public int Id { get; set; }
    public int? OfficeLocationId { get; set; }
    public string? OfficeLocation { get; set; }
    public string? MeetingRoomName { get; set; }
    public bool IsActive { get; set; }
    public string Status => IsActive ? "Active" : "InActive";
}

// MeetingRoomRequestDto
public class MeetingRoomRequestDto
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Subject is required")]
    [MaxLength(200, ErrorMessage = "Max 200 characters are allowed")]
    public string? Subject { get; set; }

    public string? Location { get; set; }
    public string? Description { get; set; }
    public bool IsAllDay { get; set; } = false;

    [Required(ErrorMessage = "Meeting Room is required")]
    public int? MeetingRoomId { get; set; }

    public string? MeetingRoomName { get; set; }
    public string? OfficeLocation { get; set; }

    [Required(ErrorMessage = "Start Time is required")]
    public DateTime? StartTime { get; set; }

    [Required(ErrorMessage = "End time is required")]
    public DateTime? EndTime { get; set; }

    public int MeetingRoomStatusId { get; set; }
    public string? MeetingRoomStatus { get; set; }

    [Required(ErrorMessage = "Office location is required")]
    public int? LocationId { get; set; }

    public string? CreatedByUserId { get; set; }
    public DateTime CreatedDate { get; set; }
    public string CssClass { get; set; } = "progress";
    public string? Reason { get; set; }
    public string? RequestedByName { get; set; }
    public string? RequestedByEmail { get; set; }
}

public enum MeetingRoomStatusEnum
{
    Pending = 1,
    Approved = 2,
    Rejected = 3,
    Cancelled = 4
}
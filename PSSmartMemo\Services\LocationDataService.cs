﻿namespace PSSmartMemo.Services;

public class LocationDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    public Task<List<LocationDto>> GetAllLocations()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.ShiftLocations
            orderby a.LocationCode, a.EmployeeShiftCode
            select new LocationDto
            {
                ShiftEnd = a.ShiftEnd,
                ShiftStart = a.ShiftStart,
                LocationCode = a.LocationCode,
                LocationTxt = a.LocationTxt,
                EmployeeShiftCode = a.EmployeeShiftCode,
                Id = a.Id
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<LocationDto> GetLocationById(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.ShiftLocations
            where a.Id == id
            select new LocationDto
            {
                ShiftEnd = a.ShiftEnd,
                ShiftStart = a.ShiftStart,
                LocationCode = a.LocationCode,
                LocationTxt = a.LocationTxt,
                EmployeeShiftCode = a.EmployeeShiftCode,
                Id = a.Id
            }).First();
        return Task.FromResult(q);
    }

    public Task<int> SaveLocation(LocationDto location)
    {
        var dc = contextFactory.CreateDbContext();
        // if location and shift code already exist then throw exception record already exist
        var q = (from a in dc.ShiftLocations
            where a.LocationCode == location.LocationCode && a.EmployeeShiftCode == location.EmployeeShiftCode &&
                  a.Id != location.Id
            select a).FirstOrDefault();
        if (q != null) throw new Exception("Record already exist");

        // if location.id == 0 then insert new record
        if (location.Id == 0)
        {
            var loc = new ShiftLocation
            {
                LocationCode = location.LocationCode,
                LocationTxt = location.LocationTxt,
                EmployeeShiftCode = location.EmployeeShiftCode,
                ShiftStart = location.ShiftStart,
                ShiftEnd = location.ShiftEnd
            };
            dc.ShiftLocations.Add(loc);
            dc.SaveChanges();
            return Task.FromResult(loc.Id);
        }
        else
        {
            // if record exist then update record otherwise throw exception record not exist
            var loc = dc.ShiftLocations.Find(location.Id);
            if (loc == null) throw new Exception("Record not exist");
            loc.LocationCode = location.LocationCode;
            loc.LocationTxt = location.LocationTxt;
            loc.EmployeeShiftCode = location.EmployeeShiftCode;
            loc.ShiftStart = location.ShiftStart;
            loc.ShiftEnd = location.ShiftEnd;
            dc.SaveChanges();
            return Task.FromResult(loc.Id);
        }
    }

    public Task<int> DeleteLocation(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var loc = dc.ShiftLocations.Find(id);
        if (loc == null) throw new Exception("Record not exist");
        dc.ShiftLocations.Remove(loc);
        dc.SaveChanges();
        return Task.FromResult(1);
    }
}
﻿using Newtonsoft.Json;
using PSSmartMemo.DTO;
using PSSmartMemo.Model;

namespace PSSmartMemo.Services;

public class EmployeeDataService
{
    private HttpClient _httpClient;
    private ApplicationDbContext _dc;
    public EmployeeDataService(HttpClient http, ApplicationDbContext dc)
    {
        _httpClient = http;
        _dc = dc;
    }
    

    private string EmpNoUpdate(string EmpNo)
    {
        if (EmpNo == @"KHI-SOFT-056\Jawaid" || EmpNo == @"NABIL-PC\Administrator")
        {
            return EmpNo;
        }
        EmpNo = EmpNo.Replace("PSMCL\\PKB0", "");

        if (long.Parse(EmpNo) >= 1 && long.Parse(EmpNo) <= 1000)
        {
            if (EmpNo.Length == 1) EmpNo = "0010000" + EmpNo;
            else if (EmpNo.Length == 2) EmpNo = "001000" + EmpNo;
            else if (EmpNo.Length == 3) EmpNo = "00100" + EmpNo;
            else if (EmpNo.Length == 4) EmpNo = "0010" + EmpNo;
            else if (EmpNo.Length == 5) EmpNo = "001" + EmpNo;
        }
        else if (long.Parse(EmpNo) >= 20001 && long.Parse(EmpNo) <= 21000) EmpNo = "001" + EmpNo;
        else if (long.Parse(EmpNo) >= 21001 && long.Parse(EmpNo) <= 29999) EmpNo = "005" + EmpNo;
        else if (long.Parse(EmpNo) >= 30001 && long.Parse(EmpNo) <= 39999) EmpNo = "001" + EmpNo;
        else if (long.Parse(EmpNo) >= 50001 && long.Parse(EmpNo) <= 69999) EmpNo = "003" + EmpNo;
        else if (long.Parse(EmpNo) >= 70001 && long.Parse(EmpNo) <= 79999) EmpNo = "003" + EmpNo;
        else if (long.Parse(EmpNo) >= 80001 && long.Parse(EmpNo) <= 89999) EmpNo = "004" + EmpNo;
        else if (long.Parse(EmpNo) >= 95001 && long.Parse(EmpNo) <= 99999) EmpNo = "006" + EmpNo;
        else if (long.Parse(EmpNo) >= 1001 && long.Parse(EmpNo) <= 9999) EmpNo = "008" + EmpNo;
        else if (long.Parse(EmpNo) >= 10001 && long.Parse(EmpNo) <= 19999) EmpNo = "008" + EmpNo;
        return EmpNo;
    }





}
